{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?62da", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?3fef", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?d9a7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?b555", "uni-app:///user/userProfile.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?23d2", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/userProfile.vue?e551"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "avatarUrl", "localNickName", "originalUserInfo", "nick<PERSON><PERSON>", "onLoad", "methods", "set", "uni", "success", "console", "loadUserInfo", "onChooseAvatar", "title", "icon", "onNickNameBlur", "uploadAvatarFile", "filePath", "name", "formData", "type", "response", "imageUrl", "saveUserInfo", "updatedUserInfo", "res", "setTimeout", "delta", "fail", "url", "userOut", "key", "val", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACkC92B;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;MACA;;MACAC;MAAA;MACAC;QACAF;QACAG;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC,yCACA;IACAC;MACAC;QACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAD;IACA;IACAE;MACAF;MACA;MACA;QACA;QACAA;MACA;QACAA;QACAF;UAAAK;UAAAC;QAAA;MACA;IACA;IACAC;MACA;MACAL;IACA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;kBAAAK;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAI;kBACAC;kBACAC;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAAA,KAQAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAZ;gBACAF;gBACAA;kBAAAK;kBAAAC;gBAAA;gBAAA,iCACAQ;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAd;gBACAE;gBACAF;kBACAK;kBACAC;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAf;kBAAAK;gBAAA;gBAAA;gBAEAZ,uCACA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAA;gBACA;cAAA;gBAGA;gBACAuB;kBACAvB;kBACAG;gBACA,GAEA;gBACAM;gBAAA;gBAAA,OACA;kBACAN;kBACAH;gBACA;cAAA;gBAHAwB;gBAIAf;;gBAEA;gBACAF;gBACAE;;gBAEA;gBACA;gBACA;gBAEAF;gBACAA;kBAAAK;kBAAAC;gBAAA;;gBAEA;gBACAY;kBACAlB;oBACAmB;oBACAlB;sBAAA;oBAAA;oBACAmB;sBACAlB;sBACAF;wBACAqB;wBACApB;0BAAA;wBAAA;wBACAmB;0BACAlB;0BACAF;4BAAAK;4BAAAC;0BAAA;wBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBACAE;gBACAF;kBACAK;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAgB;MACAtB;MACAA;MACAA;MACAA;MACAA;MACA;MACAA;MAEA;QAAAuB;QAAAC;MAAA;MACA;QAAAD;QAAAC;MAAA;MAEAxB;QAAAK;QAAAC;QAAAmB;MAAA;MAEAP;QACAlB;UACAmB;UACAlB;YAAA;UAAA;UACAmB;YACAlB;YACAF;cACAqB;cACApB;gBAAA;cAAA;cACAmB;gBACAlB;gBACAF;kBAAAK;kBAAAC;gBAAA;cACA;YACA;UACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjNA;AAAA;AAAA;AAAA;AAAykD,CAAgB,6hDAAG,EAAC,C;;;;;;;;;;;ACA7lD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/userProfile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/userProfile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userProfile.vue?vue&type=template&id=3510e5a8&\"\nvar renderjs\nimport script from \"./userProfile.vue?vue&type=script&lang=js&\"\nexport * from \"./userProfile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userProfile.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/userProfile.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=template&id=3510e5a8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"user-info\">\n      <view class=\"avatar-wrapper\">\n        <button\n          class=\"choose-avatar-button\"\n          open-type=\"chooseAvatar\"\n          @chooseavatar=\"onChooseAvatar\"\n        >\n          <image\n            class=\"avatar\"\n            :src=\"userInfo.avatarUrl || '/static/mine/default_user.png'\"\n            mode=\"aspectFill\"\n          />\n        </button>\n      </view>\n      <view class=\"nickname-section\">\n        <text class=\"label\">昵称：</text>\n        <input\n          class=\"nickname-input\"\n          type=\"nickname\"\n          placeholder=\"请输入昵称\"\n          v-model=\"localNickName\"\n          @blur=\"onNickNameBlur\"\n        />\n      </view>\n      <button class=\"save-button\" @click=\"saveUserInfo\">保存</button>\n      <button class=\"save-button\" @click=\"set\">系统设置</button>\n      <button class=\"save-button\" @click=\"userOut\">退出</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapMutations } from 'vuex';\n\nexport default {\n  data() {\n    return {\n      userInfo: {\n        avatarUrl: '', // Store the temporary or uploaded avatar URL\n      },\n      localNickName: '微信用户', // Store the temporary nickname\n      originalUserInfo: {\n        avatarUrl: '',\n        nickName: '',\n      },\n    };\n  },\n  onLoad() {\n    this.loadUserInfo();\n  },\n  methods: {\n    ...mapMutations(['updateUserItem']),\n    set() {\n      uni.openSetting({\n        success(res) {\n          console.log(res);\n        },\n      });\n    },\n    loadUserInfo() {\n      const cachedUserInfo = uni.getStorageSync('userInfo') || {};\n      this.userInfo.avatarUrl = cachedUserInfo.avatarUrl || '';\n      this.localNickName = cachedUserInfo.nickName || '微信用户';\n      this.originalUserInfo.avatarUrl = this.userInfo.avatarUrl;\n      this.originalUserInfo.nickName = this.localNickName;\n      console.log('Loaded user info:', cachedUserInfo);\n    },\n    onChooseAvatar(e) {\n      console.log('onChooseAvatar event:', e);\n      const { avatarUrl } = e.detail;\n      if (avatarUrl) {\n        this.userInfo.avatarUrl = avatarUrl;\n        console.log('Selected avatar:', avatarUrl);\n      } else {\n        console.error('Failed to get avatarUrl from event detail.');\n        uni.showToast({ title: '选择头像失败', icon: 'error' });\n      }\n    },\n    onNickNameBlur(e) {\n      this.localNickName = e.detail.value;\n      console.log('Nickname input:', this.localNickName);\n    },\n    async uploadAvatarFile(tempFilePath) {\n      uni.showLoading({ title: '上传中' });\n      try {\n        const response = await this.$api.base.uploadFile({\n          filePath: tempFilePath,\n          name: 'multipartFile',\n          formData: {\n            type: 'picture',\n          },\n        });\n\n        if (response) {\n          const imageUrl = response; // Assume response is the direct image URL, consistent with second code\n          console.log('Avatar uploaded successfully:', imageUrl);\n          uni.hideLoading();\n          uni.showToast({ title: '上传成功', icon: 'success' });\n          return imageUrl;\n        } else {\n          throw new Error('上传失败');\n        }\n      } catch (error) {\n        uni.hideLoading();\n        console.error('Upload failed:', error);\n        uni.showToast({\n          title: error.message || '上传失败，请重试',\n          icon: 'none',\n        });\n        throw error;\n      }\n    },\n    async saveUserInfo() {\n      uni.showLoading({ title: '保存中...' });\n      try {\n        let avatarUrl = this.userInfo.avatarUrl;\n        // If avatar has changed, upload it first\n        if (avatarUrl && avatarUrl !== this.originalUserInfo.avatarUrl) {\n          avatarUrl = await this.uploadAvatarFile(avatarUrl);\n          this.userInfo.avatarUrl = avatarUrl; // Update with uploaded URL\n        }\n\n        // Prepare updated user info\n        const updatedUserInfo = {\n          avatarUrl: avatarUrl || '',\n          nickName: this.localNickName,\n        };\n\n        // Update backend\n        console.log('Updating user info with:', updatedUserInfo);\n        const res = await this.$api.user.updataInfo({\n          nickName: updatedUserInfo.nickName,\n          avatarUrl: updatedUserInfo.avatarUrl,\n        });\n        console.log('Update info response:', res);\n\n        // Save to local storage\n        uni.setStorageSync('userInfo', updatedUserInfo);\n        console.log('Saved to local storage:', updatedUserInfo);\n\n        // Update original info\n        this.originalUserInfo.avatarUrl = updatedUserInfo.avatarUrl;\n        this.originalUserInfo.nickName = updatedUserInfo.nickName;\n\n        uni.hideLoading();\n        uni.showToast({ title: '保存成功', icon: 'success' });\n\n        // Navigate back or redirect\n        setTimeout(() => {\n          uni.navigateBack({\n            delta: 1,\n            success: () => console.log('Navigation back successful'),\n            fail: (err) => {\n              console.error('Navigation back failed:', err);\n              uni.redirectTo({\n                url: '/pages/mine/mine',\n                success: () => console.log('Redirected to mine page'),\n                fail: (redirectErr) => {\n                  console.error('Redirect failed:', redirectErr);\n                  uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });\n                },\n              });\n            },\n          });\n        }, 1000);\n      } catch (err) {\n        uni.hideLoading();\n        console.error('Failed to save user info:', err);\n        uni.showToast({\n          title: '保存失败: ' + (err.message || '未知错误'),\n          icon: 'error',\n        });\n      }\n    },\n    userOut() {\n      uni.removeStorageSync('token');\n      uni.removeStorageSync('userInfo');\n      uni.removeStorageSync('phone');\n      uni.removeStorageSync('avatarUrl');\n      uni.removeStorageSync('nickName');\n      // uni.removeStorageSync('shiInfo');\n      uni.removeStorageSync('userId');\n\n      this.updateUserItem({ key: 'autograph', val: '' });\n      this.updateUserItem({ key: 'userInfo', val: {} });\n\n      uni.showToast({ title: '已退出登录', icon: 'success', duration: 2000 });\n\n      setTimeout(() => {\n        uni.navigateBack({\n          delta: 1,\n          success: () => console.log('Navigation back successful'),\n          fail: (err) => {\n            console.error('Navigation back failed:', err);\n            uni.redirectTo({\n              url: '/pages/mine/mine',\n              success: () => console.log('Redirected to mine page'),\n              fail: (redirectErr) => {\n                console.error('Redirect failed:', redirectErr);\n                uni.showToast({ title: '返回失败，请手动返回', icon: 'error' });\n              },\n            });\n          },\n        });\n      }, 1000);\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\">\n.container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n  padding-top: 40rpx;\n\n  .user-info {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    background-color: #fff;\n    width: 100%;\n    padding: 40rpx;\n    border-radius: 20rpx;\n    margin-top: 40rpx;\n\n    .avatar-wrapper {\n      position: relative;\n      width: 160rpx;\n      height: 160rpx;\n      margin-bottom: 40rpx;\n\n      .choose-avatar-button {\n        width: 100%;\n        height: 100%;\n        display: block;\n        margin: 0;\n        border-radius: 50%;\n        border: none;\n        background-color: transparent;\n        overflow: hidden;\n        line-height: normal;\n\n        &::after {\n          border: none;\n        }\n\n        .avatar {\n          width: 100%;\n          height: 100%;\n          display: block;\n          border: 1px solid #ddd;\n          box-sizing: border-box;\n        }\n      }\n    }\n\n    .nickname-section {\n      display: flex;\n      align-items: center;\n      width: 100%;\n      margin-bottom: 40rpx;\n\n      .label {\n        font-size: 32rpx;\n        color: #333;\n        margin-right: 20rpx;\n        white-space: nowrap;\n      }\n\n      .nickname-input {\n        flex: 1;\n        height: 80rpx;\n        background-color: #f5f5f5;\n        border-radius: 10rpx;\n        padding: 0 20rpx;\n        font-size: 28rpx;\n        color: #333;\n        border: 1px solid #ddd;\n        box-sizing: border-box;\n        min-width: 0;\n      }\n    }\n\n    .save-button {\n      width: 100%;\n      height: 90rpx;\n      background-color: #599eff;\n      border-radius: 45rpx;\n      color: #fff;\n      font-size: 32rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-top: 20rpx;\n    }\n  }\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./userProfile.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755137414423\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}