{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mineIndex.vue?fd4d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mineIndex.vue?b5ae", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mineIndex.vue?e37c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mineIndex.vue?7d61", "uni-app:///pages/mineIndex.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mineIndex.vue?727d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mineIndex.vue?1f2f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "permissionID", "isLoading", "options", "shifusta<PERSON>", "shiInfoResponse", "tmplIds", "inviteCode", "coach_info", "orderList2", "icon", "text", "url", "count", "orderList3", "toolList2", "iconColor", "computed", "storeUserInfo", "userPageType", "mineInfo", "token", "isLoggedIn", "userInfo", "phone", "avatarUrl", "nick<PERSON><PERSON>", "userId", "pid", "status", "statusText", "statusBadgeClass", "methods", "getHighlight", "shiInfoid", "console", "role", "res", "updatedOrderList", "item", "daiFuWu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dingyue", "templateId", "templateCategoryId", "uni", "success", "title", "content", "cancelText", "confirmText", "confirmColor", "withSubscriptions", "selectedTmplIds", "fail", "fetchShifuInfo", "mobile", "<PERSON><PERSON><PERSON>", "id", "messagePush", "key", "val", "modalShownKey", "hasShownModal", "defaultUserInfo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seeinfo", "showCertificationPopup", "cancelable", "handleNavigate", "handleCallKf", "navigateTo", "callkf", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAw1B,CAAgB,w2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACsH52B;AAGA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC,UACA,gDACA,+CACA,8CACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC;QACAJ;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAG;QACAL;QACAC;QACAC;QACAI;MACA,GACA;QACAN;QACAC;QACAC;QACAI;MACA;IAEA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;QACAC,mGACA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;IACA;EAAA,EACA;EACAC,yCACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIA;kBACAR;kBACAS;gBACA;cAAA;gBAHAC;gBAIAF;gBACAG;kBAAA,uCACAC;oBACA1B,gFACAwB,wEACAG,qEACAC,uEACA;kBAAA;gBAAA,CACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAO;MAAA;MACAP;MACA;MACA;QACAA;QACA;MACA;MACA;QAAA;MAAA;MACA;MACAA;MACA;QAAA;UACAQ;UACAC;QACA;MAAA;MACAC;QACAvC;QACAwC;UACAX;UACA;YAAA;UAAA;UACA;UACA;YACAU;cACAE;cACAC;cACAC;cACAC;cACAC;cACAL;gBACAD;gBACA;kBACAA;oBACAO;kBACA;gBACA;kBACAP;gBACA;cACA;YACA;UACA;UACA;UACAQ;YACAlB;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;gBACA;cACA;cACAA;YACA;UACA;UACAA;QACA;QACAmB;UACAnB;QACA;MACA;IACA;IAEAoB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAlD;gBACA8B;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBAEAZ;kBACAiC;kBACA/B,6FACA;kBACAgC,4FACA;kBACAC;kBACA9B;kBACAC;kBACA8B;gBACA;gBAEAd;gBACA;kBACAe;kBACAC;gBACA;gBACA;gBAEAC;gBACAC;gBAEA;kBACA;kBACAlB;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;gBACA;gBACA6B;kBACAR;kBACA/B,gFACA;kBACAgC;kBACAC;kBACA9B;kBACAC;gBACA;gBAEAgB;gBACA;kBACAe;kBACAC;gBACA;gBACA;gBAEAC;gBACAC;gBAEA;kBACA;kBACAlB;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAoB;MACA;MACA;MACA9B;IACA;IACA+B;MACArB;QACAC;UACAX;QACA;MACA;IACA;IACAgC;MAAA;MACAhC;MACA;QACAU;UACAE;UACAC;UACAE;UACAD;UACAmB;UACAtB;YACA;cACA,kEACA;cACAD;gBACAjC;gBACA0C;kBACAnB;kBACAU;oBACAE;oBACArC;kBACA;gBACA;cACA;YACA;UACA;UACA4C;YACAnB;UACA;QACA;MACA;IACA;IACAkC;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;QACAxB;UACAE;UACArC;QACA;MACA;QACAmC;UACAE;UACArC;QACA;MACA;QACA;MACA;IACA;IACA4D;MACA;QACAzB;UACAE;UACArC;QACA;MACA;QACAmC;UACAE;UACArC;QACA;MACA;QACA;MACA;IACA;IACA6D;MACA;QACA1B;UACAjC;UACA0C;YACAnB;YACAU;cACAE;cACArC;YACA;UACA;QACA;MACA;QACAyB;QACAU;UACAE;UACArC;QACA;MACA;IACA;IACA8D;MACA3B;QACAE;QACArC;MACA;IACA;EAAA,EACA;EACA+D;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;gBACAtC;gBACA;gBACAU;cACA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACA6B;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAEA9B;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACniBA;AAAA;AAAA;AAAA;AAAukD,CAAgB,2hDAAG,EAAC,C;;;;;;;;;;;ACA3lD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mineIndex.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/mineIndex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mineIndex.vue?vue&type=template&id=1cb226d4&\"\nvar renderjs\nimport script from \"./mineIndex.vue?vue&type=script&lang=js&\"\nexport * from \"./mineIndex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mineIndex.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mineIndex.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mineIndex.vue?vue&type=template&id=1cb226d4&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mineIndex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mineIndex.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"pages-mine\">\n\t\t<!-- Header Section -->\n\t\t<view class=\"header\" v-if=\"!isLoading\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"avatar_view\">\n\t\t\t\t\t<image mode=\"aspectFill\" class=\"avatar\" :src=\"userInfo.avatarUrl\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t<view class=\"user-info-logged\">\n\t\t\t\t\t\t<view class=\"nickname\">\n\t\t\t\t\t\t\t{{ userInfo.nickName }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"phone-number\" v-if=\"userInfo.phone\">\n\t\t\t\t\t\t\t{{ userInfo.phone }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"status-badge\" :class=\"statusBadgeClass\" v-if=\"userInfo.status !== undefined\">\n\t\t\t\t\t\t\t{{ statusText }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view @click=\"navigateTo('../shifu/userProfile')\" class=\"settings\">\n\t\t\t\t\t<i class=\"iconfont icon-xitong text-bold\"></i>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Action Buttons Section (My Orders) -->\n\t\t<view class=\"mine-menu-list box-shadow fill-base box1\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">我的订单</view>\n\t\t\t</view>\n\t\t\t<view @click=\"dingyue()\" class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(item, index) in orderList2\" :key=\"index\"\n\t\t\t\t\t@tap=\"handleNavigate(item.url)\">\n\t\t\t\t\t<view class=\"icon-container\">\n\t\t\t\t\t\t<u-icon :name=\"item.icon\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t\t<view class=\"number-circle\" v-if=\"item.count > 0\">{{ item.count }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Commonly Used Functions -->\n\t\t<view @click=\"dingyue()\" class=\"mine-menu-list box-shadow fill-base\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">常用功能</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(item, index) in orderList3\" :key=\"index\"\n\t\t\t\t\t@tap=\"handleNavigate(item.url)\">\n\t\t\t\t\t<u-icon :name=\"item.icon\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Other Functions -->\n\t\t<view class=\"mine-menu-list box-shadow fill-base\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">其他功能</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/shifu/skills')\">\n\t\t\t\t\t<u-icon name=\"plus-square-fill\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">技能标签</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/shifu/Professiona')\">\n\t\t\t\t\t<u-icon name=\"order\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">技能证书</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/user/promotion')\">\n\t\t\t\t\t<u-icon name=\"red-packet-fill\" color=\"#E41F19\" size=\"28\"></u-icon>\n\t\t\t\t\t<view style=\"color: #E41F19;\" class=\"mt-sm\">邀请有礼</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Spacer -->\n\t\t<view class=\"spacer\"></view>\n\n\t\t<!-- Tool List Section - Modified to Grid Layout -->\n\t\t<view class=\"mine-tool-grid fill-base\">\n\t\t\t<view class=\"grid-container\">\n\t\t\t\t<view class=\"grid-item\" v-for=\"(item, index) in toolList2\" :key=\"index\" @tap=\"handleNavigate(item.url)\">\n\t\t\t\t\t<view class=\"grid-icon-container\">\n\t\t\t\t\t\t<u-icon :name=\"item.icon\" :color=\"item.iconColor\" size=\"28\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"grid-text\">{{ item.text }}</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"grid-item\" @tap=\"navigateTo('../pages/service')\">\n\t\t\t\t\t<view class=\"grid-icon-container switch-identity\">\n\t\t\t\t\t\t<u-icon name=\"man-add\" color=\"#E41F19\" size=\"28\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"color: #E41F19;\" class=\"grid-text\">切换用户版</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"grid-item\">\n\t\t\t\t\t<button class=\"contact-btn-wrapper\" open-type=\"contact\" bindcontact=\"handleContact\"\n\t\t\t\t\t\tsession-from=\"sessionFrom\">\n\t\t\t\t\t\t<view class=\"grid-icon-container switch-identity\">\n\t\t\t\t\t\t\t<u-icon name=\"server-man\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"grid-text\">客服</view>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Tabbar -->\n\t\t<tabbar cur=\"1\"></tabbar>\n\t</view>\n</template>\n\n<script>\n\timport tabbar from \"@/components/tabbarsf.vue\";\n\timport {\n\t\tmapState,\n\t\tmapMutations\n\t} from \"vuex\";\n\n\texport default {\n\t\tcomponents: {\n\t\t\ttabbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tpermissionID: '',\n\t\t\t\tisLoading: true,\n\t\t\t\toptions: {},\n\t\t\t\tshifustatus: '',\n\t\t\t\tshiInfoResponse: null, // New reactive state for shiInfoResponse\n\t\t\t\ttmplIds: [\n\t\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\tinviteCode: '',\n\t\t\t\tcoach_info: {},\n\t\t\t\torderList2: [{\n\t\t\t\t\t\ticon: 'order',\n\t\t\t\t\t\ttext: '全部',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=0',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'bell',\n\t\t\t\t\t\ttext: '待上门',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=3',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'hourglass-half-fill',\n\t\t\t\t\t\ttext: '待服务',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=5',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'clock',\n\t\t\t\t\t\ttext: '服务中',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=6',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'thumb-up',\n\t\t\t\t\t\ttext: '已完成',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=7',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'chat-fill',\n\t\t\t\t\t\ttext: '售后',\n\t\t\t\t\t\turl: '/shifu/master_my_order?tab=8',\n\t\t\t\t\t\tcount: 0\n\t\t\t\t\t},\n\t\t\t\t],\n\t\t\t\torderList3: [{\n\t\t\t\t\t\ticon: 'red-packet',\n\t\t\t\t\t\ttext: '服务收入',\n\t\t\t\t\t\turl: '/shifu/income'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'file-text-fill',\n\t\t\t\t\t\ttext: '报价列表',\n\t\t\t\t\t\turl: '/shifu/master_bao_list'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'rmb-circle',\n\t\t\t\t\t\ttext: '保证金',\n\t\t\t\t\t\turl: '/shifu/Margin'\n\t\t\t\t\t}\n\t\t\t\t],\n\t\t\t\ttoolList2: [{\n\t\t\t\t\t\ticon: 'plus-people-fill',\n\t\t\t\t\t\ttext: '师傅入驻',\n\t\t\t\t\t\turl: '/shifu/Settle',\n\t\t\t\t\t\ticonColor: '#448cfb'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\ticon: 'edit-pen',\n\t\t\t\t\t\ttext: '编辑师傅资料',\n\t\t\t\t\t\turl: '/shifu/master_Info',\n\t\t\t\t\t\ticonColor: '#448cfb'\n\t\t\t\t\t},\n\t\t\t\t]\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t...mapState({\n\t\t\t\tstoreUserInfo: state => state.user.userInfo || {},\n\t\t\t\tuserPageType: state => state.user.userPageType,\n\t\t\t\tmineInfo: state => state.user.mineInfo,\n\t\t\t\ttoken: state => state.user.autograph || ''\n\t\t\t}),\n\t\t\tisLoggedIn() {\n\t\t\t\treturn !!this.token;\n\t\t\t},\n\t\t\tuserInfo() {\n\t\t\t\tconst shiInfo = this.shiInfoResponse || (uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {});\n\t\t\t\treturn {\n\t\t\t\t\tphone: shiInfo.mobile || this.storeUserInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\tavatarUrl: shiInfo.avatarUrl || this.storeUserInfo.avatarUrl || uni.getStorageSync('avatarUrl') ||\n\t\t\t\t\t\t'/static/mine/default_user.png',\n\t\t\t\t\tnickName: shiInfo.coachName || this.storeUserInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\tuserId: shiInfo.id || this.storeUserInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\tpid: shiInfo.pid || this.storeUserInfo.pid || uni.getStorageSync('pid') || '',\n\t\t\t\t\tstatus: Number(shiInfo.status) !== undefined ? Number(shiInfo.status) : -1\n\t\t\t\t};\n\t\t\t},\n\t\t\tstatusText() {\n\t\t\t\tswitch (this.shifustatus) {\n\t\t\t\t\tcase -1:\n\t\t\t\t\t\treturn '未入驻师傅';\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\treturn '审核中';\n\t\t\t\t\tcase 2:\n\t\t\t\t\t\treturn '已认证';\n\t\t\t\t\tcase 4:\n\t\t\t\t\t\treturn '审核驳回';\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t},\n\t\t\tstatusBadgeClass() {\n\t\t\t\treturn {\n\t\t\t\t\t'status-not-registered': this.userInfo.status === -1,\n\t\t\t\t\t'status-pending': this.userInfo.status === 1,\n\t\t\t\t\t'status-approved': this.userInfo.status === 2,\n\t\t\t\t\t'status-rejected': this.userInfo.status === 4\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t...mapMutations(['updateUserItem']),\n\t\t\tasync getHighlight() {\n\t\t\t\tconst shiInfoid = this.shiInfoResponse || (uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : null);\n\t\t\t\tif (!shiInfoid || !shiInfoid.id) {\n\t\t\t\t\tconsole.log('No userId, skipping getHighlight');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.service.getHighlight({\n\t\t\t\t\t\tuserId: shiInfoid.id,\n\t\t\t\t\t\trole: 2\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('getHighlight response:', res);\n\t\t\t\t\tconst updatedOrderList = this.orderList2.map((item, index) => ({\n\t\t\t\t\t\t...item,\n\t\t\t\t\t\tcount: index === 0 ? (res && res.countOrder ? res.countOrder : 0) : index === 1 ? (\n\t\t\t\t\t\t\tres && res.daiShangMen ? res.daiShangMen : 0) : index === 2 ? (res && res\n\t\t\t\t\t\t\t.daiFuWu ? res.daiFuWu : 0) : index === 3 ? (res && res.fuWuZhong ? res\n\t\t\t\t\t\t\t.fuWuZhong : 0) : index === 4 ? (res && res.yiWanCheng ? res.yiWanCheng :\n\t\t\t\t\t\t\t0) : index === 5 ? (res && res.shouHou ? res.shouHou : 0) : 0\n\t\t\t\t\t}));\n\t\t\t\t\tthis.orderList2 = updatedOrderList;\n\t\t\t\t} catch (err) {\n\t\t\t\t\tconsole.error('getHighlight error:', err);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tdingyue() {\n\t\t\t\tconsole.log('dingyue called');\n\t\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());\n\t\t\t\tconst selectedTmplIds = shuffled.slice(0, 3);\n\t\t\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\t\ttemplateId: id,\n\t\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t\t}));\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\tconst hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');\n\t\t\t\t\t\tconst hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');\n\t\t\t\t\t\tif (hasRejection && !hasShownModal) {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收用户订单通知。',\n\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\tconfirmText: '去开启',\n\t\t\t\t\t\tconfirmColor: '#007AFF',\n\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\tuni.setStorageSync('hasShownSubscriptionModal', true);\n\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\twithSubscriptions: true\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else if (modalRes.cancel) {\n\t\t\t\t\t\t\t\tuni.setStorageSync('hasCanceledSubscription', true);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t\tasync fetchShifuInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tthis.isLoading = true;\n\t\t\t\t\tconst shiInfoResponse = await this.$api.shifu.getMaster();\n\t\t\t\t\tconsole.log(shiInfoResponse);\n\t\t\t\t\tif (!shiInfoResponse || typeof shiInfoResponse !== 'object') {\n\t\t\t\t\t\tthrow new Error('获取师傅状态失败: 响应数据无效');\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.shiInfoResponse = shiInfoResponse; // Store response in reactive state\n\n\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\tmobile: shiInfoResponse.mobile || '',\n\t\t\t\t\t\tavatarUrl: shiInfoResponse.avatarUrl || this.storeUserInfo.avatarUrl || uni.getStorageSync(\n\t\t\t\t\t\t\t'avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\tcoachName: shiInfoResponse.coachName || this.storeUserInfo.nickName || uni.getStorageSync(\n\t\t\t\t\t\t\t'nickName') || '微信用户',\n\t\t\t\t\t\tid: shiInfoResponse.id || this.storeUserInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tpid: this.storeUserInfo.pid || uni.getStorageSync('pid') || '',\n\t\t\t\t\t\tstatus: Number(shiInfoResponse.status) || -1,\n\t\t\t\t\t\tmessagePush: Number(shiInfoResponse.messagePush) || -1\n\t\t\t\t\t};\n\n\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify(userInfo));\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'shiInfo',\n\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t});\n\t\t\t\t\tthis.getshifustatus();\n\n\t\t\t\t\tconst modalShownKey = `certificationModalShown_${userInfo.id}_${userInfo.status}`;\n\t\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\n\t\t\t\t\tif (!hasShownModal && (userInfo.status === -1 || userInfo.status === 4)) {\n\t\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('fetchShifuInfo error:', error);\n\t\t\t\t\tthis.shiInfoResponse = null; // Reset on error\n\t\t\t\t\tconst defaultUserInfo = {\n\t\t\t\t\t\tmobile: '',\n\t\t\t\t\t\tavatarUrl: this.storeUserInfo.avatarUrl || uni.getStorageSync('avatarUrl') ||\n\t\t\t\t\t\t\t'/static/mine/default_user.png',\n\t\t\t\t\t\tcoachName: this.storeUserInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\tid: this.storeUserInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\tpid: this.storeUserInfo.pid || uni.getStorageSync('pid') || '',\n\t\t\t\t\t\tstatus: -1\n\t\t\t\t\t};\n\n\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify(defaultUserInfo));\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'shiInfo',\n\t\t\t\t\t\tval: defaultUserInfo\n\t\t\t\t\t});\n\t\t\t\t\tthis.getshifustatus();\n\n\t\t\t\t\tconst modalShownKey = `certificationModalShown_${defaultUserInfo.id}_${defaultUserInfo.status}`;\n\t\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\n\t\t\t\t\tif (!hasShownModal && defaultUserInfo.status === -1) {\n\t\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetshifustatus() {\n\t\t\t\tconst shiInfo = this.shiInfoResponse || (uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {});\n\t\t\t\tthis.shifustatus = shiInfo.status;\n\t\t\t\tconsole.log(shiInfo);\n\t\t\t},\n\t\t\tseeinfo() {\n\t\t\t\tuni.openSetting({\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tconsole.log(res);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tshowCertificationPopup() {\n\t\t\t\tconsole.log(this.userInfo.status);\n\t\t\t\tif (this.userInfo.status === -1 || this.userInfo.status === 4) {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: this.userInfo.status === -1 ? '您尚未成为师傅，是否前往认证？' : '您的师傅认证被驳回，是否重新认证？',\n\t\t\t\t\t\tconfirmText: '去认证',\n\t\t\t\t\t\tcancelText: '再想想',\n\t\t\t\t\t\tcancelable: true,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tconst targetUrl = this.userInfo.status === -1 ? '/shifu/Settle' :\n\t\t\t\t\t\t\t\t\t'/shifu/Settle';\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: targetUrl,\n\t\t\t\t\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t\t\t\t\tconsole.error('Navigation to certification failed:', err);\n\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转认证页面失败',\n\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('Modal failed:', err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleNavigate(url) {\n\t\t\t\tif (url === '/shifu/Settle') {\n\t\t\t\t\tthis.navigateTo(url);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (url === '/user/promotion') {\n\t\t\t\t\tthis.navigateTo(url);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (url === '/shifu/master_Info') {\n\t\t\t\t\tthis.navigateTo(url);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '你还不是师傅',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (this.shifustatus === 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '师傅状态在审核中',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (this.shifustatus === 2) {\n\t\t\t\t\tthis.navigateTo(url);\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleCallKf() {\n\t\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '你还不是师傅',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (this.shifustatus === 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '师傅状态在审核中',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (this.shifustatus === 2) {\n\t\t\t\t\tthis.callkf();\n\t\t\t\t}\n\t\t\t},\n\t\t\tnavigateTo(url) {\n\t\t\t\tif (url) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: url,\n\t\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t\tconsole.error('Navigation failed:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('Navigation URL is empty or invalid');\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tcallkf() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '联系客服功能待实现',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tasync onLoad(options) {\n\t\t\tif (options.inviteCode) {\n\t\t\t\tconsole.log('Received inviteCode:', options.inviteCode);\n\t\t\t\tthis.inviteCode = options.inviteCode;\n\t\t\t\tuni.setStorageSync('receivedInviteCode', options.inviteCode);\n\t\t\t}\n\t\t\tthis.options = options;\n\t\t\tawait this.fetchShifuInfo();\n\t\t\tawait this.getHighlight();\n\t\t},\n\t\tasync onShow() {\n\t\t\tawait this.fetchShifuInfo();\n\t\t\tawait this.getHighlight();\n\t\t},\n\t\tasync onPullDownRefresh() {\n\t\t\ttry {\n\t\t\t\tawait this.fetchShifuInfo();\n\t\t\t\tawait this.getHighlight();\n\t\t\t} finally {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\">\n\t.floating-contact {\n\t\tposition: fixed;\n\t\tbottom: 470rpx;\n\t\tright: 30rpx;\n\t\tz-index: 1000;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 50rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n\t\tpadding: 10rpx 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-container {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-btn {\n\t\tbackground: none;\n\t\tborder: none;\n\t\tcolor: #576b95;\n\t\tfont-size: 30rpx;\n\t\tline-height: 1.5;\n\t\tpadding: 10rpx 20rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.contact-btn:active {\n\t\tbackground-color: #ededee;\n\t}\n\n\t.pages-mine {\n\t\tmin-height: 100vh;\n\t\tpadding-bottom: 120rpx;\n\n\t\t.header {\n\t\t\theight: 292rpx;\n\t\t\tbackground-color: #599EFF;\n\t\t\tposition: relative;\n\n\t\t\t.header-content {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 40rpx 30rpx 0;\n\t\t\t\tposition: relative;\n\n\t\t\t\t.avatar_view {\n\t\t\t\t\twidth: 120rpx;\n\t\t\t\t\theight: 120rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t.avatar {\n\t\t\t\t\t\twidth: 100%;\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.user-info {\n\t\t\t\t\tmargin-left: 20rpx;\n\t\t\t\t\tcolor: #fff;\n\n\t\t\t\t\t.user-info-logged {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\tflex-direction: column;\n\t\t\t\t\t\tgap: 10rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.nickname {\n\t\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tgap: 10rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.phone-number {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\topacity: 0.9;\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-badge {\n\t\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\t\tpadding: 8rpx 20rpx;\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tline-height: 1.2;\n\t\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\t\twidth: fit-content;\n\t\t\t\t\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-not-registered {\n\t\t\t\t\t\tbackground-color: #b0b0b0;\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-pending {\n\t\t\t\t\t\tbackground-color: #f4b400;\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-approved {\n\t\t\t\t\t\tbackground-color: #f5a623;\n\t\t\t\t\t}\n\n\t\t\t\t\t.status-rejected {\n\t\t\t\t\t\tbackground-color: #f44336;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.settings {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tright: 30rpx;\n\t\t\t\t\ttop: 100rpx;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 60rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\n\t\t\t\t\t.icon-xitong {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.box1 {\n\t\t\tmargin-top: -20rpx;\n\t\t\tborder-radius: 36rpx 36rpx 0 0;\n\t\t\tposition: relative;\n\t\t\tz-index: 10;\n\t\t}\n\n\t\t.mine-menu-list {\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 0 20rpx;\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\n\t\t\t.menu-title {\n\t\t\t\theight: 90rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 0 30rpx 0 40rpx;\n\t\t\t\tborder-bottom: 1px solid #f0f0f0;\n\n\t\t\t\t.f-paragraph {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.flex-warp {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tpadding: 10rpx 0;\n\n\t\t\t\t.order-item {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\twidth: 33.3%;\n\t\t\t\t\tpadding-bottom: 10rpx;\n\t\t\t\t\tmargin-top: 15rpx;\n\t\t\t\t\tfont-size: 25rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\ttransition: transform 0.2s;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t}\n\n\t\t\t\t\t.icon-container {\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t}\n\n\t\t\t\t\t.number-circle {\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: -10rpx;\n\t\t\t\t\t\tright: -5rpx;\n\t\t\t\t\t\twidth: 30rpx;\n\t\t\t\t\t\theight: 30rpx;\n\t\t\t\t\t\tbackground-color: #ff4d4f;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: center;\n\t\t\t\t\t\tjustify-content: center;\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.mt-sm {\n\t\t\t\t\tmargin-top: 16rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.spacer {\n\t\t\theight: 20rpx;\n\t\t\tbackground-color: transparent;\n\t\t}\n\n\t\t.mine-tool-grid {\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 0 20rpx 30rpx;\n\t\t\tborder-radius: 12rpx;\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\t\tpadding: 30rpx;\n\n\t\t\t.grid-container {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\tgap: 20rpx;\n\t\t\t}\n\n\t\t\t.grid-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\twidth: calc(33.33% - 20rpx);\n\t\t\t\tmin-width: 140rpx;\n\t\t\t\ttransition: transform 0.2s ease;\n\n\t\t\t\t&:active {\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t}\n\n\t\t\t\t.grid-icon-container {\n\t\t\t\t\twidth: 80rpx;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t&.switch-identity {\n\t\t\t\t\t\t/* Specific styling for switch-identity icon */\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.grid-text {\n\t\t\t\t\tfont-size: 25rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tline-height: 1.2;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t}\n\n\t\t\t\t.grid-subtitle {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #A1A1A1;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.flex-between {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t}\n\t}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mineIndex.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mineIndex.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755133959679\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}