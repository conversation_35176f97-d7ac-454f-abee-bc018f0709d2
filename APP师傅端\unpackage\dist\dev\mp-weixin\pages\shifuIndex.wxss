@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-5a79bfc8 {
  min-height: 100vh;
  overflow: auto;
  background-color: #f3f4f5;
  padding-bottom: 120rpx;
}
.page .img.data-v-5a79bfc8 {
  width: 690rpx;
  margin: 20rpx auto;
}
.page .location-bar.data-v-5a79bfc8 {
  display: flex;
  padding: 20rpx;
  border: 1rpx solid #eeeeee;
  color: #999;
  font-size: 28rpx;
  background-color: #fff;
}
.page .subscription.data-v-5a79bfc8 {
  flex-shrink: 0;
}
.page .location-info.data-v-5a79bfc8 {
  flex: 1;
  text-align: left;
}
.page .location-info .location-text.data-v-5a79bfc8 {
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 1.4;
}
.page .tabs-container.data-v-5a79bfc8 {
  background-color: #fff;
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
}
.page .custom-tabs.data-v-5a79bfc8 {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}
.page .custom-tabs .tab-item.data-v-5a79bfc8 {
  flex: 1;
  padding: 25rpx 10rpx;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.page .custom-tabs .tab-item .tab-text.data-v-5a79bfc8 {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
  transition: color 0.3s ease;
}
.page .custom-tabs .tab-item .tab-badge.data-v-5a79bfc8 {
  position: absolute;
  top: 10rpx;
  right: 15rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  min-width: 20rpx;
  height: 24rpx;
  line-height: 20rpx;
  text-align: center;
}
.page .custom-tabs .tab-item.active .tab-text.data-v-5a79bfc8 {
  color: #2E80FE;
  font-weight: 600;
}
.page .custom-tabs .tab-item.active.data-v-5a79bfc8::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #2E80FE;
  border-radius: 2rpx;
}
.page .filter-container.data-v-5a79bfc8 {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  position: relative;
  z-index: 10;
}
.page .filter-bar.data-v-5a79bfc8 {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  position: relative;
  z-index: 10;
}
.page .filter-item-container.data-v-5a79bfc8 {
  flex: 1;
  position: relative;
}
.page .filter-item.data-v-5a79bfc8 {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}
.page .filter-item.active-filter-item.data-v-5a79bfc8 {
  color: #2E80FE;
  font-weight: 600;
}
.page .filter-item text.data-v-5a79bfc8 {
  margin-right: 10rpx;
}
.page .arrow.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: #999;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.page .arrow.rotate.data-v-5a79bfc8 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.page .filter-dropdown.data-v-5a79bfc8 {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-width: none;
  -webkit-transform: none;
          transform: none;
  background-color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  z-index: 999;
  border-radius: 0 0 12rpx 12rpx;
}
.page .dropdown-content.data-v-5a79bfc8 {
  padding: 30rpx;
  max-height: 80vh;
  overflow-y: auto;
}
.page .filter-section.data-v-5a79bfc8 {
  margin-bottom: 30rpx;
}
.page .section-title.data-v-5a79bfc8 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.page .option-list.data-v-5a79bfc8 {
  display: flex;
  flex-wrap: wrap;
}
.page .option-item.data-v-5a79bfc8 {
  padding: 15rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #666;
}
.page .option-item.active.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
  border: 1px solid #2E80FE;
}
.page .custom-price-inputs.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
}
.page .custom-price-inputs input.data-v-5a79bfc8 {
  flex: 1;
  height: 70rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  background-color: #f5f5f5;
}
.page .custom-price-inputs text.data-v-5a79bfc8 {
  margin: 0 20rpx;
  color: #999;
}
.page .distance-input.data-v-5a79bfc8 {
  padding: 20rpx 0;
}
.page .distance-input > text.data-v-5a79bfc8 {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 20rpx;
}
.page .distance-input .distance-input-container.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e9ecef;
}
.page .distance-input .distance-input-container input.data-v-5a79bfc8 {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}
.page .distance-input .distance-input-container .unit.data-v-5a79bfc8 {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
  flex-shrink: 0;
}
.page .distance-input .distance-hint.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
}
.page .filter-actions.data-v-5a79bfc8 {
  display: flex;
  padding: 20rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}
.page .filter-btn.data-v-5a79bfc8 {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.page .filter-btn.reset.data-v-5a79bfc8 {
  background-color: #fff;
  color: #666;
  border: 1rpx solid #ccc;
}
.page .filter-btn.confirm.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
  margin-left: 20rpx;
}
.page .quotation-counts.data-v-5a79bfc8 {
  margin: 10rpx 20rpx;
  padding: 16rpx 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}
.page .quotation-counts .counts-header.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}
.page .quotation-counts .counts-header .counts-title.data-v-5a79bfc8 {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  position: relative;
}
.page .quotation-counts .counts-header .counts-title.data-v-5a79bfc8::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 30rpx;
  height: 2rpx;
  background: linear-gradient(90deg, #2E80FE, #4A9EFF);
  border-radius: 1rpx;
}
.page .quotation-counts .counts-row.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.page .quotation-counts .counts-row .count-item.data-v-5a79bfc8 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page .quotation-counts .counts-row .count-item .count-label.data-v-5a79bfc8 {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
  font-weight: 500;
}
.page .quotation-counts .counts-row .count-item .count-value.data-v-5a79bfc8 {
  font-size: 24rpx;
  font-weight: 700;
  color: #2E80FE;
  line-height: 1.2;
}
.page .quotation-counts .counts-row .count-divider.data-v-5a79bfc8 {
  width: 1rpx;
  height: 40rpx;
  background-color: #e8e8e8;
  margin: 0 20rpx;
}
.page .check_box.data-v-5a79bfc8 {
  margin: 20rpx auto;
  width: 690rpx;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}
.page .check_box .collapse-container.data-v-5a79bfc8 {
  width: 100%;
}
.page .check_box .collapse-item.data-v-5a79bfc8 {
  border-bottom: 1rpx solid #eee;
}
.page .check_box .collapse-header.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fff;
}
.page .check_box .collapse-title.data-v-5a79bfc8 {
  font-weight: 500;
}
.page .check_box .collapse-icon.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: #999;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.page .check_box .collapse-icon.rotate.data-v-5a79bfc8 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.page .check_box .collapse-content.data-v-5a79bfc8 {
  padding: 20rpx 30rpx;
  background-color: #f8f8f8;
}
.page .check_box .price-range.data-v-5a79bfc8,
.page .check_box .distance-range.data-v-5a79bfc8,
.page .check_box .cate-list.data-v-5a79bfc8 {
  width: 100%;
}
.page .check_box .price-options.data-v-5a79bfc8 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}
.page .check_box .price-options .price-option.data-v-5a79bfc8 {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  padding: 15rpx 0;
  text-align: center;
  background-color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.page .check_box .price-options .price-option.active.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
}
.page .check_box .custom-price.data-v-5a79bfc8 {
  margin-top: 20rpx;
}
.page .check_box .custom-price .custom-price-title.data-v-5a79bfc8 {
  font-size: 28rpx;
  margin-bottom: 15rpx;
}
.page .check_box .custom-price .custom-price-inputs.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
}
.page .check_box .custom-price .custom-price-inputs input.data-v-5a79bfc8 {
  flex: 1;
  height: 70rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
}
.page .check_box .custom-price .custom-price-inputs text.data-v-5a79bfc8 {
  margin: 0 20rpx;
}
.page .check_box .distance-slider.data-v-5a79bfc8 {
  padding: 20rpx 0;
}
.page .check_box .distance-slider text.data-v-5a79bfc8 {
  font-size: 28rpx;
  display: block;
  margin-bottom: 20rpx;
}
.page .check_box .distance-slider .distance-labels.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.page .check_box .cate-list.data-v-5a79bfc8 {
  display: flex;
  flex-wrap: wrap;
}
.page .check_box .cate-list .cate-item.data-v-5a79bfc8 {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  padding: 15rpx 0;
  text-align: center;
  background-color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.page .check_box .cate-list .cate-item.active.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
}
.page .check_box .filter-actions.data-v-5a79bfc8 {
  display: flex;
  padding: 20rpx 0;
}
.page .check_box .filter-actions .filter-btn.data-v-5a79bfc8 {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
}
.page .check_box .filter-actions .filter-btn.primary.data-v-5a79bfc8 {
  background-color: #2E80FE;
  color: #fff;
}
.page .check_box .filter-actions .filter-btn.plain.data-v-5a79bfc8 {
  background-color: #fff;
  color: #2E80FE;
  border: 1rpx solid #2E80FE;
}
.page .modal-content.data-v-5a79bfc8 {
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}
.page .re_item.data-v-5a79bfc8 {
  width: 690rpx;
  background-color: #fff;
  margin: 20rpx auto;
  padding: 40rpx;
  border-radius: 10rpx;
}
.page .re_item .top.data-v-5a79bfc8 {
  display: flex;
}
.page .re_item .top image.data-v-5a79bfc8 {
  margin-right: 20rpx;
}
.page .re_item .top .order.data-v-5a79bfc8 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}
.page .re_item .top .order .title.data-v-5a79bfc8 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .re_item .top .order .price.data-v-5a79bfc8 {
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .re_item .info.data-v-5a79bfc8 {
  margin-top: 40rpx;
}
.page .re_item .info .address.data-v-5a79bfc8,
.page .re_item .info .tel.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.page .re_item .info .address.data-v-5a79bfc8:last-child,
.page .re_item .info .tel.data-v-5a79bfc8:last-child {
  margin-bottom: 0;
}
.page .re_item .info .address .left.data-v-5a79bfc8,
.page .re_item .info .tel .left.data-v-5a79bfc8 {
  margin-right: 20rpx;
  flex-shrink: 0;
}
.page .re_item .info .address .right.data-v-5a79bfc8,
.page .re_item .info .tel .right.data-v-5a79bfc8 {
  flex: 1;
}
.page .re_item .info .address .right .address_name.data-v-5a79bfc8,
.page .re_item .info .tel .right .address_name.data-v-5a79bfc8 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  max-width: 100%;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
}
.page .re_item .info .address .right .address_Info.data-v-5a79bfc8,
.page .re_item .info .tel .right .address_Info.data-v-5a79bfc8 {
  margin-top: 8rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: #666666;
  max-width: 100%;
  word-wrap: break-word;
  word-break: break-all;
  line-height: 1.4;
}
.page .re_item .info .tel .right.data-v-5a79bfc8 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .re_item .notes.data-v-5a79bfc8 {
  background-color: #f2f3f4;
  border-radius: 5rpx;
  padding: 15rpx;
  margin-top: 20rpx;
  font-size: 26rpx;
  line-height: 1.5;
}
.page .re_item .notes view.data-v-5a79bfc8 {
  color: #999999;
  margin-bottom: 5rpx;
}
.page .re_item .order-stats.data-v-5a79bfc8 {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
}
.page .re_item .order-stats .stats-row.data-v-5a79bfc8 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}
.page .re_item .order-stats .stats-row.data-v-5a79bfc8:last-child {
  margin-bottom: 0;
}
.page .re_item .order-stats .stats-row .stat-item.data-v-5a79bfc8 {
  display: flex;
  align-items: center;
  flex: 1;
}
.page .re_item .order-stats .stats-row .stat-item .stat-label.data-v-5a79bfc8 {
  font-size: 24rpx;
  color: #666666;
  margin-right: 8rpx;
}
.page .re_item .order-stats .stats-row .stat-item .stat-value.data-v-5a79bfc8 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333333;
}
.page .re_item .btn.data-v-5a79bfc8 {
  margin: 40rpx auto 0;
  width: 100%;
  height: 82rpx;
  border-radius: 12rpx;
  border: 2rpx solid #2E80FE;
  line-height: 82rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .loadmore.data-v-5a79bfc8 {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
}
.page .footer.data-v-5a79bfc8 {
  color: #333;
  margin: 20rpx 0;
  text-align: center;
  font-size: 24rpx;
}

