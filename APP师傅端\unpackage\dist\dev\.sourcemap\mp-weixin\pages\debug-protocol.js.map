{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-protocol.vue?76d3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-protocol.vue?7848", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-protocol.vue?e3f5", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-protocol.vue?2a4c", "uni-app:///pages/debug-protocol.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-protocol.vue?9611", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-protocol.vue?7479"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "isLoading", "logs", "apiResponse", "onLoad", "methods", "goBack", "uni", "addLog", "message", "type", "console", "clearLogs", "testGetConfig", "$api", "response", "fields", "error", "testGetLoginProtocol", "testNavigateToService", "url", "success", "fail", "testNavigateToPrivacy"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA61B,CAAgB,62BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8Dj3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACAC;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;MACA;MACA;QACA;MACA;MACAC;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBACA;gBACA;gBAEA;;gBAEA;gBACA;kBACA;gBACA;kBACA;gBACA;gBAEA;kBACA;gBACA;kBACA;gBACA;;gBAEA;gBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;kBAAAC;gBAAA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAJ;cAAA;gBAAAC;gBACA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACA;kBAAAE;gBAAA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAE;MAAA;MACA;MAEA;QACA;QACA;QAEAZ;UACAa;UACAC;YACA;UACA;UACAC;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MAEA;QACA;QACA;QAEAhB;UACAa;UACAC;YACA;UACA;UACAC;YACA;UACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxNA;AAAA;AAAA;AAAA;AAAomD,CAAgB,wjDAAG,EAAC,C;;;;;;;;;;;ACAxnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/debug-protocol.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/debug-protocol.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./debug-protocol.vue?vue&type=template&id=0d67308b&scoped=true&\"\nvar renderjs\nimport script from \"./debug-protocol.vue?vue&type=script&lang=js&\"\nexport * from \"./debug-protocol.vue?vue&type=script&lang=js&\"\nimport style0 from \"./debug-protocol.vue?vue&type=style&index=0&id=0d67308b&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0d67308b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/debug-protocol.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-protocol.vue?vue&type=template&id=0d67308b&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.apiResponse ? JSON.stringify(_vm.apiResponse, null, 2) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-protocol.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-protocol.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"debug-page\">\n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"nav-icon\">‹</text>\n      </view>\n      <view class=\"nav-title\">协议调试</view>\n      <view class=\"nav-right\"></view>\n    </view>\n\n    <!-- 测试按钮 -->\n    <view class=\"test-section\">\n      <view class=\"test-title\">API测试</view>\n      \n      <button class=\"test-btn\" @click=\"testGetConfig\" :disabled=\"isLoading\">\n        测试getConfig接口\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testGetLoginProtocol\" :disabled=\"isLoading\">\n        测试getLoginProtocol接口\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testNavigateToService\" :disabled=\"isLoading\">\n        测试跳转服务协议\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testNavigateToPrivacy\" :disabled=\"isLoading\">\n        测试跳转隐私政策\n      </button>\n      \n      <button class=\"test-btn\" @click=\"clearLogs\">\n        清空日志\n      </button>\n    </view>\n\n    <!-- API响应显示 -->\n    <view class=\"response-section\" v-if=\"apiResponse\">\n      <view class=\"response-title\">API响应数据</view>\n      <view class=\"response-content\">\n        <text class=\"response-text\">{{ JSON.stringify(apiResponse, null, 2) }}</text>\n      </view>\n    </view>\n\n    <!-- 日志显示 -->\n    <view class=\"log-section\">\n      <view class=\"log-title\">调试日志</view>\n      <view class=\"log-content\">\n        <text \n          class=\"log-item\" \n          v-for=\"(log, index) in logs\" \n          :key=\"index\"\n          :class=\"log.type\"\n        >\n          {{ log.message }}\n        </text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport $api from '@/api/index.js'\n\nexport default {\n  name: 'DebugProtocol',\n  data() {\n    return {\n      isLoading: false,\n      logs: [],\n      apiResponse: null\n    }\n  },\n  onLoad() {\n    this.addLog('=== 协议调试页面加载完成 ===', 'info')\n  },\n  methods: {\n    /**\n     * 返回上一页\n     */\n    goBack() {\n      uni.navigateBack()\n    },\n\n    /**\n     * 添加日志\n     */\n    addLog(message, type = 'info') {\n      const time = new Date().toLocaleTimeString()\n      this.logs.unshift({\n        message: `[${time}] ${message}`,\n        type: type\n      })\n      if (this.logs.length > 100) {\n        this.logs = this.logs.slice(0, 100)\n      }\n      console.log(`[DEBUG] ${message}`)\n    },\n\n    /**\n     * 清空日志\n     */\n    clearLogs() {\n      this.logs = []\n      this.apiResponse = null\n    },\n\n    /**\n     * 测试getConfig接口\n     */\n    async testGetConfig() {\n      this.isLoading = true\n      this.addLog('=== 测试getConfig接口 ===', 'info')\n      \n      try {\n        const response = await $api.base.getConfig()\n        this.addLog(`getConfig接口调用成功`, 'success')\n        this.addLog(`响应数据: ${JSON.stringify(response)}`, 'info')\n        \n        this.apiResponse = response\n        \n        // 检查数据结构\n        if (response.loginProtocol) {\n          this.addLog(`发现loginProtocol字段，长度: ${response.loginProtocol.length}`, 'success')\n        } else {\n          this.addLog(`未发现loginProtocol字段`, 'warning')\n        }\n        \n        if (response.content) {\n          this.addLog(`发现content字段，长度: ${response.content.length}`, 'success')\n        } else {\n          this.addLog(`未发现content字段`, 'warning')\n        }\n        \n        // 列出所有字段\n        const fields = Object.keys(response)\n        this.addLog(`响应包含字段: ${fields.join(', ')}`, 'info')\n        \n      } catch (error) {\n        this.addLog(`getConfig接口调用失败: ${error.message}`, 'error')\n        this.apiResponse = { error: error.message }\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    /**\n     * 测试getLoginProtocol接口\n     */\n    async testGetLoginProtocol() {\n      this.isLoading = true\n      this.addLog('=== 测试getLoginProtocol接口 ===', 'info')\n      \n      try {\n        const response = await $api.base.getLoginProtocol()\n        this.addLog(`getLoginProtocol接口调用成功`, 'success')\n        this.addLog(`响应数据: ${JSON.stringify(response)}`, 'info')\n        \n        this.apiResponse = response\n        \n      } catch (error) {\n        this.addLog(`getLoginProtocol接口调用失败: ${error.message}`, 'error')\n        this.apiResponse = { error: error.message }\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    /**\n     * 测试跳转服务协议\n     */\n    testNavigateToService() {\n      this.addLog('=== 测试跳转服务协议 ===', 'info')\n      \n      try {\n        const url = '../user/configuser?type=service'\n        this.addLog(`跳转URL: ${url}`, 'info')\n        \n        uni.navigateTo({\n          url: url,\n          success: () => {\n            this.addLog('跳转服务协议成功', 'success')\n          },\n          fail: (error) => {\n            this.addLog(`跳转服务协议失败: ${JSON.stringify(error)}`, 'error')\n          }\n        })\n      } catch (error) {\n        this.addLog(`跳转服务协议异常: ${error.message}`, 'error')\n      }\n    },\n\n    /**\n     * 测试跳转隐私政策\n     */\n    testNavigateToPrivacy() {\n      this.addLog('=== 测试跳转隐私政策 ===', 'info')\n      \n      try {\n        const url = '../user/configuser?type=privacy'\n        this.addLog(`跳转URL: ${url}`, 'info')\n        \n        uni.navigateTo({\n          url: url,\n          success: () => {\n            this.addLog('跳转隐私政策成功', 'success')\n          },\n          fail: (error) => {\n            this.addLog(`跳转隐私政策失败: ${JSON.stringify(error)}`, 'error')\n          }\n        })\n      } catch (error) {\n        this.addLog(`跳转隐私政策异常: ${error.message}`, 'error')\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.debug-page {\n  min-height: 100vh;\n  background: #f5f5f5;\n}\n\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 24rpx;\n  background: #fff;\n  border-bottom: 1rpx solid #eee;\n  \n  .nav-left, .nav-right {\n    width: 80rpx;\n  }\n  \n  .nav-icon {\n    font-size: 36rpx;\n    color: #333;\n    font-weight: bold;\n  }\n  \n  .nav-title {\n    font-size: 32rpx;\n    color: #333;\n    font-weight: 500;\n  }\n}\n\n.test-section, .response-section {\n  background: #fff;\n  margin: 20rpx;\n  border-radius: 12rpx;\n  padding: 32rpx 24rpx;\n}\n\n.test-title, .response-title {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20rpx;\n}\n\n.test-btn {\n  width: 100%;\n  height: 80rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 30rpx;\n  font-weight: 500;\n  margin-bottom: 16rpx;\n  \n  &:disabled {\n    background: #ccc;\n  }\n  \n  &:active:not(:disabled) {\n    opacity: 0.8;\n  }\n}\n\n.response-content {\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  padding: 20rpx;\n  max-height: 400rpx;\n  overflow-y: auto;\n}\n\n.response-text {\n  font-size: 24rpx;\n  color: #333;\n  line-height: 1.6;\n  word-break: break-all;\n  white-space: pre-wrap;\n}\n\n.log-section {\n  background: #fff;\n  margin: 0 20rpx 20rpx;\n  border-radius: 12rpx;\n  padding: 32rpx 24rpx;\n}\n\n.log-title {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20rpx;\n}\n\n.log-content {\n  max-height: 600rpx;\n  overflow-y: auto;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  padding: 20rpx;\n}\n\n.log-item {\n  display: block;\n  font-size: 24rpx;\n  line-height: 1.6;\n  margin-bottom: 8rpx;\n  word-break: break-all;\n  \n  &.info {\n    color: #666;\n  }\n  \n  &.success {\n    color: #52c41a;\n  }\n  \n  &.warning {\n    color: #faad14;\n  }\n  \n  &.error {\n    color: #f5222d;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-protocol.vue?vue&type=style&index=0&id=0d67308b&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-protocol.vue?vue&type=style&index=0&id=0d67308b&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755140700513\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}