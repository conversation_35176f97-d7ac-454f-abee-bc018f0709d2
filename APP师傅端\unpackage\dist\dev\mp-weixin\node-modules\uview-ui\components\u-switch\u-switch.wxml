<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="{{['u-switch','data-v-4a8c9de7',disabled&&'u-switch--disabled']}}" style="{{$root.s0}}" bindtap="__e"><view class="u-switch__bg data-v-4a8c9de7" style="{{$root.s1}}"></view><view data-ref="u-switch__node" class="{{['u-switch__node','data-v-4a8c9de7','vue-ref',value&&'u-switch__node--on']}}" style="{{$root.s2}}"><u-loading-icon vue-id="54485f66-1" show="{{loading}}" mode="circle" timingFunction="linear" color="{{value?activeColor:'#AAABAD'}}" size="{{size*0.6}}" class="data-v-4a8c9de7" bind:__l="__l"></u-loading-icon></view></view>