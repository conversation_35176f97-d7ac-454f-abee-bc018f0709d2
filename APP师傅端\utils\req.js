let hasHandled401 = false; // Global flag to track 401 handling

// 接口返回值code说明
// 200=>正常；
// 400=>报错；
// 401=>需要登陆；
// 402=>错误并且弹出报错，报错内容为 error；
// 403=>错误并且弹出报错，报错内容为 error(小程序跳转到个人中心)；
// 404=>错误并且弹出报错，报错内容为 error(小程序跳转到首页)；


import $api from '@/api/index.js';
import $util from './index.js';
import siteInfo from '../siteinfo.js';
import $store from "@/store/index.js";



import {
  networkError,
  serverError
} from './error.js';

// 添加finally方法,用于指定不管 Promise 对象最后状态如何，都会执行的操作
Promise.prototype.finally = function (callback) {
  let P = this.constructor;
  return this.then(
    value => P.resolve(callback()).then(() => value),
    reason => P.resolve(callback()).then(() => { throw reason; })
  );
};



const formatUrl = function (url) {
  return `${siteInfo.siteroot}${url}`;
};

// 阿里云地址转为本地域名的 
const formatImageUrl = function (url) {
  return url.includes(siteInfo.siteroot) ? url :
    `${formatUrl("card/getImage")}&path=${encodeURIComponent(url)}`;
};

// 微信小程序登录
const wxLogin = async function () {
  let { query } = await uni.getLaunchOptionsSync();
  console.log('query', query);
  let url = formatUrl("index/login");
  try {
    uni.showLoading({ title: "登录中..." });
    let [providerErr, providerData] = await uni.getProvider({ service: 'oauth' });
    let [loginErr, loginData] = await uni.login({ provider: providerData.provider[0] });

    let login_param = {
      code: loginData.code,
      pid: query.scene
    };

    // 使用 uni.request 替代 tokenFly.post
    let [requestErr, requestRes] = await uni.request({
      url: url,
      method: 'POST',
      data: login_param,
      header: {
        "content-type": "application/json"
      }
    });

    if (requestErr) {
      uni.hideLoading();
      serverError({ code: 0, msg: '网络请求失败' });
      throw requestErr;
    }

    let { code, data, error } = requestRes.data;

    if (code !== 200) {
      uni.hideLoading();
      serverError({ code, msg: error });
      throw requestRes;
    }

    // 登录成功
    uni.hideLoading();
    $store.commit('updateUserItem', { key: 'userInfo', val: data.data });
    $store.commit('updateUserItem', { key: 'autograph', val: data.autograph });
    return data;
  } catch (e) {
    uni.hideLoading();
    if (e.data && e.data.code !== 200) {
      serverError({ code: e.data.code, msg: e.data.error });
    }
    return await Promise.reject(e);
  }
};

// 获取当前平台类型
const getPlatformType = function() {
  // #ifdef APP-PLUS
  return 1; // APP
  // #endif

  // #ifdef MP-WEIXIN
  return 0; // 微信小程序
  // #endif

  // #ifdef H5
  return 0; // H5当作小程序处理
  // #endif

  // 默认返回小程序
  return 0;
};









// 统一处理请求,satus=200网络正常code=200服务器正常
const httpType = ["post", "get", "delete"];
const formatReq = function () {
  let req = {};
  httpType.forEach((type) => {
    req[type] = async function (url, param) {
      console.log(url, type);
      url = formatUrl(url);

      // 动态获取平台类型
      const isapp = getPlatformType();
      console.log('当前平台类型 isapp:', isapp);

      return new Promise((resolve, reject) => {
        try {
          uni.request({
            method: type,
            url,
            data: param,
            header: {
              "content-type": "application/json",
              isapp: isapp,
			  platform:1,//师傅段
              autograph: $store.state.user.autograph || '',
              // Cookies: $store.state.user.autograph || '',
            },
            async success(e) {
              let data = e.data;
              console.log('---', data, url);
              // 响应拦截
              if (data.status === 401&&!url.includes('activityOrder/commentActivityOrder')&&!url.includes('activityOrder/selectActivityConfig')&&!url.includes('activityOrder/selectCount')&& !url.includes('user/info')&& !url.includes('agents/list')&& !url.includes('shiFu/index/shiFuAuth')) {
                if (!hasHandled401) {
                  hasHandled401 = true;
                  console.log('401 error: Clearing state and navigating to /pages/mine');

                  // Clear Vuex user state immediately
                  $store.commit('updateUserItem', { key: 'userInfo', val: {} });
                  $store.commit('updateUserItem', { key: 'autograph', val: '' });

                  // Clear local storage
                  ['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid', 'shiInfo'].forEach(key => {
                    uni.removeStorageSync(key);
                  });

                  // Show login prompt
                  uni.showToast({
                    title: '登录已失效，请重新登录',
                    icon: 'none',
                    duration: 2000
                  });

                  // Navigate to mine.vue after a short delay
                  setTimeout(() => {
                    uni.reLaunch({
                      url: '/pages/mine',
                      success: () => {
                        console.log('Navigated to /pages/mine');
                        hasHandled401 = false; // Reset flag
                      },
                      fail: (err) => {
                        console.error('Navigation failed:', err);
                        hasHandled401 = false;
                      }
                    });
                  }, 1000);
                }
                return reject('登录失效');
              }
              if (data.code == 400) {
                uni.showToast({
                  icon: 'none',
                  title: data.error
                });
                return;
              }
   if (data.code != 401) {
       resolve(data); // Always resolve with the entire 'data' object
       return;
   }

              //#ifdef  MP-WEIXIN
              await wxLogin();
              //#endif
            },
            fail(e) {
              console.log('fail', e);
              uni.showModal({
                content: '当前无网络，请刷新',
                confirmText: '刷新',
                cancelText: '关闭',
                success: function (e) {
                  if (e.confirm) {
                    uni.reLaunch({
                      url: '/pages/service'
                    });
                  }
                }
              });
            }
          });
        } catch (err) {
          console.log('err', err);
        }
      });
    };
  });
  return req;
};
const req = formatReq();

// 定义上传,picture--代表图片 audio--音频 video--视频,默认picture
const uploadFile = async (url, {
  name = "file",
  filePath,
  header = {
    autograph: $store.state.user.autograph || '',
	 platform:1,//师傅段
	isapp: getPlatformType(), // 动态获取平台类型
  },
  formData = {
    type: 'picture'
  }
} = {}) => {
  url = formatUrl(url);
  let [, res] = await uni.uploadFile({
    url,
    filePath,
    name,
    formData,
    header,
  });

  if (res.statusCode != 200) {
    $util.hideAll();
    networkError();
    return await Promise.reject(res);
  }
  let parseData = JSON.parse(res.data);
  let { code, msg, data } = parseData;
  if (code != 200) {
    $util.hideAll();
    serverError({ code, msg });
    return await Promise.reject(res);
  }
  return data;
};

export {
  req,
  uploadFile,
  formatImageUrl,
  formatUrl,
  getPlatformType
};