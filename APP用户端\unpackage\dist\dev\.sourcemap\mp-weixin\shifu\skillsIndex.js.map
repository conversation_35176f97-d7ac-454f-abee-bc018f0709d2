{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skillsIndex.vue?2a11", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skillsIndex.vue?236d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skillsIndex.vue?d175", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skillsIndex.vue?721c", "uni-app:///shifu/skillsIndex.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skillsIndex.vue?7028", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/skillsIndex.vue?bf96"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "args", "timeout", "fn", "data", "keyword", "categories", "selectedCategoryId", "expandedSubCategories", "loading", "shifuInfo", "serviceIds", "error", "dataBox", "isSaving", "computed", "currentCategory", "console", "methods", "selectCategory", "toggleSubCategory", "toggleSelectService", "selectedItems", "count", "isServiceSelected", "getSelectedCount", "selectAllServices", "allServiceIds", "isAllSelected", "saveSettings", "serviceIdsString", "uni", "serviceNames", "category", "subCategory", "serviceNamesString", "title", "icon", "duration", "success", "setTimeout", "delta", "debouncedSaveSettings", "goUrl", "url", "getList", "$api", "response", "categoriesData", "Array", "loadSavedSelections", "split", "map", "filter", "getInfoS", "res", "serviceIdsArray", "created", "onLoad", "city"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA01B,CAAgB,02BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACkG92B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;EACA;EACA;IAAA;IAAA;MAAAC;IAAA;IACA;IACAC;MACAA;MACAC;IACA;EACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;QAAAC;MAAA;MAAA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MAAA;MACA;MACA;QAAA;MAAA;MACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAF;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;MACA;QACA;MACA;;MAEA;IACA;IAEA;IACAG;MACAH;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAI;MACAJ;;MAEA;MACA;QACA;UAAAN;QAAA;MACA;;MAEA;MACA;QACA;UACAW;UACAC;QACA;MACA;MAEA;MAEA;QACA;QACA;UACA;QACA;MACA;QACA;QACA;UAAA;QAAA;MACA;;MAEA;MACA;MAEAN;MACA;IACA;IAEA;IACAO;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MAEA;QAAA;MAAA;MACA;;MAEA;MACA;QACA;UACAJ;UACAC;QACA;MACA;MAEA;QACA;QACA;QACA;UAAA;QAAA;MACA;QACA;QACAI;UACA;YACA;UACA;UACA;YACA;UACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACA;kBACAC;kBACAC;kBACAd;;kBAEA;kBACAe;kBACA;oBACA;sBACAC;wBACA;0BACAC;4BACA;8BACAF;4BACA;0BACA;wBACA;sBACA;oBACA;kBACA;;kBAEA;kBACAG;kBACAJ;kBACAd;kBAEAc;oBACAK;oBACAC;oBACAC;oBACAC;sBACAC;wBACAT;0BAAAU;wBAAA;sBACA;oBACA;kBACA;gBACA;kBACAV;oBACAK;oBACAC;kBACA;kBACApB;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAyB;IAAA;IAEAC;MACAZ;QAAAa;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA9B;;gBAEA;gBACA+B;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBACAD;gBAAA;gBAAA;cAAA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAGA;gBACAA;kBACA;kBACAf;oBACA;oBACA;oBACA;sBACA;wBACAX;wBACAC;sBACA;oBACA;kBACA;gBACA;gBAEA;gBACAN;gBAEA;kBACA;kBACA;oBACA;kBACA;kBACAD;oBAAA;kBAAA;kBACA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAC;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiC;MAAA;MACA;QACA;QACA;UACA,sCACAC,WACAC;YAAA;UAAA,GACAC;YAAA;UAAA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;UACA;YACApB;cACA;gBACA;kBACAX;kBACAC;gBACA;gBACA;kBAAA,OACAW;oBAAA;kBAAA;gBAAA,EACA;gBACA;gBACA;cACA;YACA;UACA;QACA;QAEAjB;QACAA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAqC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAR;cAAA;gBAAAS;gBACAtC;;gBAEA;gBACA;kBAAAN;gBAAA;;gBAEA;gBACA6C;gBACA;kBACAA,8CACAL,WACAC;oBAAA;kBAAA,GACAC;oBAAA;kBAAA;gBACA;kBACAG;oBAAA;kBAAA;oBAAA;kBAAA;gBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAEAvC;;gBAEA;gBACA;gBACA;kBACA;oBACAgB;sBACA;wBACA;0BACAX;0BACAC;wBACA;wBACA;0BAAA,OACAW;4BAAA;0BAAA;wBAAA,EACA;wBACA;wBACA;sBACA;oBACA;kBACA;gBACA;gBAEAjB;gBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;kBAAAN;gBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACA8C;IACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEAC;cACA1C;cACA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAA;cACAc;gBACAK;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC/eA;AAAA;AAAA;AAAA;AAAsuC,CAAgB,ytCAAG,EAAC,C;;;;;;;;;;;ACA1vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/skillsIndex.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/skillsIndex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./skillsIndex.vue?vue&type=template&id=6c7d5886&scoped=true&\"\nvar renderjs\nimport script from \"./skillsIndex.vue?vue&type=script&lang=js&\"\nexport * from \"./skillsIndex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./skillsIndex.vue?vue&type=style&index=0&id=6c7d5886&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6c7d5886\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/skillsIndex.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=template&id=6c7d5886&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && !_vm.error ? _vm.categories.length : null\n  var g1 =\n    !_vm.loading && !_vm.error\n      ? _vm.currentCategory &&\n        _vm.currentCategory.children &&\n        _vm.currentCategory.children.length\n      : null\n  var l1 =\n    !_vm.loading && !_vm.error && g1\n      ? _vm.__map(_vm.currentCategory.children, function (subCategory, __i1__) {\n          var $orig = _vm.__get_orig(subCategory)\n          var m0 = _vm.getSelectedCount(subCategory.id)\n          var m1 = _vm.isAllSelected(subCategory.id)\n          var g2 = _vm.expandedSubCategories.includes(subCategory.id)\n          var g3 =\n            _vm.expandedSubCategories.includes(subCategory.id) &&\n            subCategory.serviceList &&\n            subCategory.serviceList.length\n          var l0 = g3\n            ? _vm.__map(subCategory.serviceList, function (service, __i2__) {\n                var $orig = _vm.__get_orig(service)\n                var m2 = _vm.isServiceSelected(service.id, subCategory.id)\n                return {\n                  $orig: $orig,\n                  m2: m2,\n                }\n              })\n            : null\n          var g4 = !g3\n            ? _vm.expandedSubCategories.includes(subCategory.id) &&\n              (!subCategory.serviceList || !subCategory.serviceList.length)\n            : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n            g2: g2,\n            g3: g3,\n            l0: l0,\n            g4: g4,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=script&lang=js&\"", "\n<template>\n  <view class=\"page\">\n    <view class=\"main\">\n      <view class=\"left\">\n        <scroll-view scroll-y=\"true\" class=\"scrollL\">\n          <view v-if=\"loading\" class=\"loading\">\n            <text>加载中...</text>\n          </view>\n          <view v-else-if=\"error\" class=\"error\">\n            <text>{{ error }}</text>\n          </view>\n          <view v-else-if=\"!categories.length\" class=\"no-content\">\n            <text>暂无分类数据</text>\n          </view>\n          <view\n            v-else\n            class=\"left_item\"\n            v-for=\"category in categories\"\n            :key=\"category.id\"\n            @tap=\"selectCategory(category.id)\"\n            :class=\"{ active: selectedCategoryId === category.id }\"\n          >\n            <view class=\"category_name\">{{ category.name }}</view>\n          </view>\n        </scroll-view>\n      </view>\n\n      <view class=\"right\">\n        <scroll-view scroll-y=\"true\" class=\"scrollR\">\n          <view v-if=\"loading\" class=\"loading\">\n            <text>加载中...</text>\n          </view>\n          <view v-else-if=\"error\" class=\"error\">\n            <text>{{ error }}</text>\n          </view>\n          <view v-else-if=\"currentCategory && currentCategory.children && currentCategory.children.length\">\n            <view \n              class=\"subcategory_section\" \n              v-for=\"subCategory in currentCategory.children\" \n              :key=\"subCategory.id\"\n            >\n              <view class=\"subcategory_header\">\n                <view class=\"subcategory_title\" @click=\"toggleSubCategory(subCategory.id)\">\n                  {{ subCategory.name }} \n                  <text class=\"selected_count\">(已选择{{ getSelectedCount(subCategory.id) }})</text>\n                </view>\n                <view class=\"select_all\" @click=\"selectAllServices(subCategory.id)\">\n                  {{ isAllSelected(subCategory.id) ? '取消全选' : '全选' }}\n                </view>\n                <view class=\"expand_icon\" @click=\"toggleSubCategory(subCategory.id)\">\n                  {{ expandedSubCategories.includes(subCategory.id) ? '▲' : '▼' }}\n                </view>\n              </view>\n              \n              <view \n                class=\"service_items\" \n                v-if=\"expandedSubCategories.includes(subCategory.id) && subCategory.serviceList && subCategory.serviceList.length\"\n              >\n                <view \n                  class=\"service_item\" \n                  v-for=\"service in subCategory.serviceList\" \n                  :key=\"service.id\"\n                  @click=\"toggleSelectService(service.id, subCategory.id)\"\n                  :class=\"{ active: isServiceSelected(service.id, subCategory.id) }\"\n                >\n                  {{ service.title }}\n                </view>\n              </view>\n              \n              <view \n                class=\"no-services\" \n                v-else-if=\"expandedSubCategories.includes(subCategory.id) && (!subCategory.serviceList || !subCategory.serviceList.length)\"\n              >\n                <text>暂无服务项目</text>\n              </view>\n            </view>\n          </view>\n          <view v-else class=\"no-content\">\n            <text>暂无子分类</text>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n    <view class=\"footer\">\n      <button \n        class=\"save_btn\" \n        @click=\"debouncedSaveSettings\" \n        :disabled=\"isSaving\"\n        :class=\"{ disabled: isSaving }\"\n      >\n        {{ isSaving ? '保存中...' : '保存设置' }}\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport $api from \"@/api/index.js\";\n\n// Debounce function\nconst debounce = (fn, wait) => {\n  let timeout = null;\n  return function (...args) {\n    if (timeout) clearTimeout(timeout); // Clear existing timeout\n    timeout = setTimeout(() => {\n      timeout = null;\n      fn.apply(this, args);\n    }, wait);\n  };\n};\n\nexport default {\n  data() {\n    return {\n      keyword: \"\",\n      categories: [],\n      selectedCategoryId: null,\n      expandedSubCategories: [], // Stores IDs of expanded subcategories\n      loading: false,\n      shifuInfo: { serviceIds: [] }, // Initialize with default serviceIds\n      error: null,\n      dataBox: {}, // Stores user-selected service items, grouped by subcategory ID\n      isSaving: false, // Save status\n    };\n  },\n  computed: {\n    currentCategory() {\n      if (!this.selectedCategoryId) return null;\n      const category = this.categories.find(cat => cat.id === this.selectedCategoryId);\n      console.log(\"Current category:\", category);\n      return category;\n    }\n  },\n  methods: {\n    // Select parent category\n    selectCategory(id) {\n      console.log(\"选择父类分类:\", id);\n      this.selectedCategoryId = id;\n      \n      // Initially expand the first subcategory\n      const category = this.categories.find(cat => cat.id === id);\n      if (category && category.children && category.children.length > 0) {\n        this.expandedSubCategories = [category.children[0].id];\n      } else {\n        this.expandedSubCategories = []; // Clear if no children\n      }\n      \n      this.$forceUpdate();\n    },\n    \n    // Toggle subcategory expand/collapse state\n    toggleSubCategory(subCategoryId) {\n      console.log(\"切换子类展开状态:\", subCategoryId);\n      const index = this.expandedSubCategories.indexOf(subCategoryId);\n      \n      if (index === -1) {\n        this.expandedSubCategories.push(subCategoryId);\n      } else {\n        this.expandedSubCategories.splice(index, 1);\n      }\n    },\n    \n    // Toggle service item selection state\n    toggleSelectService(serviceId, subCategoryId) {\n      console.log(\"切换服务选择状态:\", serviceId, subCategoryId);\n      \n      // Ensure shifuInfo is initialized\n      if (!this.shifuInfo) {\n        this.shifuInfo = { serviceIds: [] };\n      }\n      \n      // Ensure the subcategory exists in dataBox\n      if (!this.dataBox[subCategoryId]) {\n        this.$set(this.dataBox, subCategoryId, {\n          selectedItems: [],\n          count: 0\n        });\n      }\n      \n      const index = this.dataBox[subCategoryId].selectedItems.indexOf(serviceId);\n      \n      if (index === -1) {\n        this.dataBox[subCategoryId].selectedItems.push(serviceId);\n        if (!this.shifuInfo.serviceIds.includes(serviceId)) {\n          this.shifuInfo.serviceIds.push(serviceId);\n        }\n      } else {\n        this.dataBox[subCategoryId].selectedItems.splice(index, 1);\n        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => id !== serviceId);\n      }\n      \n      // Update count\n      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;\n      \n      console.log(\"Updated shifuInfo.serviceIds:\", this.shifuInfo.serviceIds);\n      this.$forceUpdate();\n    },\n    \n    // Check if service is selected\n    isServiceSelected(serviceId, subCategoryId) {\n      if (!this.dataBox[subCategoryId]) return false;\n      return this.dataBox[subCategoryId].selectedItems.includes(serviceId);\n    },\n    \n    // Get the number of selected services for a subcategory\n    getSelectedCount(subCategoryId) {\n      if (!this.dataBox[subCategoryId]) return 0;\n      return this.dataBox[subCategoryId].count || 0;\n    },\n    \n    // Select all/deselect all service items\n    selectAllServices(subCategoryId) {\n      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);\n      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return;\n\n      const allServiceIds = subCategory.serviceList.map(service => service.id);\n      const isAllCurrentlySelected = this.isAllSelected(subCategoryId);\n\n      // Ensure dataBox entry exists for this subCategory\n      if (!this.dataBox[subCategoryId]) {\n        this.$set(this.dataBox, subCategoryId, {\n          selectedItems: [],\n          count: 0\n        });\n      }\n\n      if (isAllCurrentlySelected) {\n        // Deselect all\n        this.dataBox[subCategoryId].selectedItems = [];\n        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => !allServiceIds.includes(id));\n      } else {\n        // Select all\n        allServiceIds.forEach(serviceId => {\n          if (!this.dataBox[subCategoryId].selectedItems.includes(serviceId)) {\n            this.dataBox[subCategoryId].selectedItems.push(serviceId);\n          }\n          if (!this.shifuInfo.serviceIds.includes(serviceId)) {\n            this.shifuInfo.serviceIds.push(serviceId);\n          }\n        });\n      }\n\n      // Update count\n      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;\n      this.$forceUpdate();\n    },\n    \n    // Check if all service items are selected\n    isAllSelected(subCategoryId) {\n      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);\n      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return false;\n      const allServiceIds = subCategory.serviceList.map(service => service.id);\n      const selectedServiceIds = this.dataBox[subCategoryId]?.selectedItems || [];\n      return allServiceIds.length > 0 && allServiceIds.every(id => selectedServiceIds.includes(id));\n    },\n    \n    // Save settings\n    async saveSettings() {\n      this.isSaving = true; // Set saving status\n      try {\n        // Save shifuInfo.serviceIds as a comma-separated string\n        const serviceIdsString = this.shifuInfo.serviceIds.join(\",\");\n        uni.setStorageSync(\"selectedServices\", serviceIdsString);\n        console.log(\"Saved selectedServices:\", serviceIdsString);\n        \n        // Collect service names for selected serviceIds\n        const serviceNames = [];\n        this.categories.forEach(category => {\n          if (category.children && category.children.length) {\n            category.children.forEach(subCategory => {\n              if (subCategory.serviceList && subCategory.serviceList.length) {\n                subCategory.serviceList.forEach(service => {\n                  if (this.shifuInfo.serviceIds.includes(service.id)) {\n                    serviceNames.push(service.title);\n                  }\n                });\n              }\n            });\n          }\n        });\n        \n        // Save service names as a comma-separated string\n        const serviceNamesString = serviceNames.join(\",\");\n        uni.setStorageSync(\"selectedServiceNames\", serviceNamesString);\n        console.log(\"Saved selectedServiceNames:\", serviceNamesString);\n        \n        uni.showToast({\n          title: \"保存成功\",\n          icon: \"success\",\n          duration: 2000,\n          success: () => {\n            setTimeout(() => {\n              uni.navigateBack({ delta: 1 });\n            }, 2000);\n          },\n        });\n      } catch (e) {\n        uni.showToast({\n          title: \"保存失败\",\n          icon: \"none\",\n        });\n        console.error(\"保存失败:\", e);\n      } finally {\n        this.isSaving = false; // Reset button status\n      }\n    },\n    \n    // Debounced saveSettings\n    debouncedSaveSettings: null, // Initialized to null, set in created hook\n    \n    goUrl(url) {\n      uni.navigateTo({ url });\n    },\n    \n    // Get category list\n    async getList() {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await $api.shifu.getSkill();\n        console.log(\"API Response:\", response);\n\n        // Process response data\n        let categoriesData = [];\n        if (Array.isArray(response)) {\n          categoriesData = response;\n        } else if (response.data && Array.isArray(response.data)) {\n          categoriesData = response.data;\n        } else {\n          throw new Error(\"无效或空的数据\");\n        }\n        \n        // Ensure children and serviceList exist, and initialize dataBox\n        categoriesData.forEach(category => {\n          if (!category.children) category.children = [];\n          category.children.forEach(subCategory => {\n            if (!subCategory.serviceList) subCategory.serviceList = [];\n            // Only initialize dataBox entry if it doesn't already exist\n            if (!this.dataBox[subCategory.id]) {\n              this.$set(this.dataBox, subCategory.id, {\n                selectedItems: [],\n                count: 0,\n              });\n            }\n          });\n        });\n        \n        this.categories = categoriesData;\n        console.log(\"Categories processed:\", this.categories);\n        \n        if (this.categories.length > 0) {\n          // If selectedCategoryId is not set (e.g., on initial load), set it to the first category\n          if (this.selectedCategoryId === null) {\n            this.selectedCategoryId = this.categories[0].id;\n          }\n          const currentCategory = this.categories.find(cat => cat.id === this.selectedCategoryId);\n          if (currentCategory && currentCategory.children && currentCategory.children.length > 0) {\n            // Only set expandedSubCategories if it's empty or doesn't include the first child\n            if (this.expandedSubCategories.length === 0 || !this.expandedSubCategories.includes(currentCategory.children[0].id)) {\n                this.expandedSubCategories = [currentCategory.children[0].id];\n            }\n          }\n        } else {\n          this.error = \"分类数据为空\";\n        }\n      } catch (err) {\n        this.error = \"数据加载失败: \" + err.message;\n        console.error(\"Error in getList:\", err);\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    // Load saved selections (only as a fallback if API fails)\n    loadSavedSelections() {\n      try {\n        const savedData = uni.getStorageSync('selectedServices');\n        if (savedData && savedData.trim()) {\n          this.shifuInfo.serviceIds = savedData\n            .split(',')\n            .map(id => parseInt(id.trim(), 10))\n            .filter(id => !isNaN(id));\n        } else {\n          this.shifuInfo.serviceIds = [];\n        }\n        \n        // Reconstruct dataBox from serviceIds based on the currently loaded categories\n        // Ensure categories are loaded before this part is executed effectively\n        this.dataBox = {}; // Clear previous dataBox before reconstructing\n        this.categories.forEach(category => {\n          if (category.children && category.children.length) {\n            category.children.forEach(subCategory => {\n              if (subCategory.serviceList && subCategory.serviceList.length) {\n                this.$set(this.dataBox, subCategory.id, {\n                  selectedItems: [],\n                  count: 0\n                });\n                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>\n                  subCategory.serviceList.some(service => service.id === serviceId)\n                );\n                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;\n                this.dataBox[subCategory.id].count = matchingServiceIds.length;\n              }\n            });\n          }\n        });\n        \n        console.log(\"Loaded shifuInfo.serviceIds from storage:\", this.shifuInfo.serviceIds);\n        console.log(\"Reconstructed dataBox from loaded storage:\", this.dataBox);\n        this.$forceUpdate();\n      } catch (e) {\n        console.error('加载已保存选择失败:', e);\n        this.shifuInfo.serviceIds = [];\n      }\n    },\n    \n    // Get and initialize service IDs\n    async getInfoS() {\n      try {\n        const res = await $api.shifu.getSInfo();\n        console.log(\"getSInfo Response:\", res);\n        \n        // Initialize shifuInfo\n        this.shifuInfo = res && typeof res === 'object' ? res : { serviceIds: [] };\n        \n        // Always use API serviceIds if available\n        let serviceIdsArray = [];\n        if (typeof this.shifuInfo.serviceIds === 'string' && this.shifuInfo.serviceIds.trim() !== '') {\n          serviceIdsArray = this.shifuInfo.serviceIds\n            .split(',')\n            .map(id => parseInt(id.trim(), 10))\n            .filter(id => !isNaN(id));\n        } else if (Array.isArray(this.shifuInfo.serviceIds)) {\n          serviceIdsArray = this.shifuInfo.serviceIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));\n        }\n        this.shifuInfo.serviceIds = serviceIdsArray;\n        \n        // If API provides no valid serviceIds, try local storage\n        if (!this.shifuInfo.serviceIds.length) {\n          this.loadSavedSelections();\n        }\n        \n        console.log(\"Processed Service IDs:\", this.shifuInfo.serviceIds);\n\n        // Update dataBox based on shifuInfo.serviceIds after categories are loaded\n        this.dataBox = {}; // Clear dataBox to reconstruct based on API data\n        this.categories.forEach(category => {\n          if (category.children && category.children.length) {\n            category.children.forEach(subCategory => {\n              if (subCategory.serviceList && subCategory.serviceList.length) {\n                this.$set(this.dataBox, subCategory.id, {\n                  selectedItems: [],\n                  count: 0\n                });\n                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>\n                  subCategory.serviceList.some(service => service.id === serviceId)\n                );\n                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;\n                this.dataBox[subCategory.id].count = matchingServiceIds.length;\n              }\n            });\n          }\n        });\n\n        console.log(\"Updated dataBox after getInfoS:\", this.dataBox);\n        console.log(\"Updated shifuInfo.serviceIds after getInfoS:\", this.shifuInfo.serviceIds);\n        this.$forceUpdate();\n      } catch (err) {\n        console.error(\"Error in getInfoS:\", err);\n        this.shifuInfo = { serviceIds: [] };\n        this.loadSavedSelections(); // Fallback to local storage on API failure\n      }\n    }\n  },\n  created() {\n    // Initialize debounce function here\n    this.debouncedSaveSettings = debounce(this.saveSettings, 1000);\n  },\n  async onLoad() {\n    try {\n      const city = uni.getStorageSync(\"city\");\n      console.log(\"City:\", city);\n      // Clear selectedServices to start fresh only if you want to explicitly clear it\n      // uni.setStorageSync('selectedServices', ''); \n      await this.getList();\n      await this.getInfoS();\n    } catch (err) {\n      console.error(\"Error in onLoad:\", err);\n      uni.showToast({\n        title: \"页面加载失败\",\n        icon: \"none\"\n      });\n    }\n  },\n};\n</script>\n\n<style scoped>\n.page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f8f8f8;\n  padding-bottom: 120rpx; /* Reserve space for fixed footer */\n  box-sizing: border-box; /* Ensures padding is included in height */\n}\n\n.main {\n  flex: 1;\n  display: flex;\n  overflow: hidden; /* Important for scroll-views inside */\n}\n\n.left {\n  width: 190rpx;\n  background-color: #f8f8f8;\n  flex-shrink: 0; /* Prevent it from shrinking */\n}\n\n.scrollL {\n  height: 100%;\n  overflow-y: auto;\n}\n\n.left_item {\n  padding: 0 20rpx;\n  min-height: 100rpx;\n  font-size: 28rpx;\n  font-weight: 500;\n  color: #333;\n  border-left: 6rpx solid transparent;\n  transition: all 0.2s;\n  display: flex;\n  flex-direction: column;\n  justify-content: center; /* Center content vertically */\n}\n\n.left_item.active {\n  color: #2e80fe;\n  font-size: 30rpx;\n  border-left-color: #2e80fe;\n  background-color: #fff;\n}\n\n.category_name {\n  height: 100rpx; /* Ensure consistent height for names */\n  width: 100%;\n  display: flex;\n  align-items: center;\n}\n\n.right {\n  flex: 1;\n  background-color: #fff;\n  border-radius: 12rpx 12rpx 0 0;\n  margin-left: 10rpx;\n  overflow: hidden; /* Important for scroll-views inside */\n}\n\n.scrollR {\n  height: 100%;\n  overflow-y: auto;\n}\n\n.subcategory_section {\n  margin-bottom: 15rpx;\n}\n\n.subcategory_header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20rpx 30rpx;\n  background-color: #fafafa;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.subcategory_title {\n  font-size: 30rpx;\n  font-weight: 500;\n  color: #333;\n  flex: 1; /* Allow title to take available space */\n}\n\n.selected_count {\n  color: #2e80fe;\n  font-weight: normal;\n  margin-left: 10rpx; /* Add some spacing */\n}\n\n.select_all {\n  font-size: 26rpx;\n  color: #2e80fe;\n  margin-left: 20rpx;\n  flex-shrink: 0; /* Prevent it from shrinking */\n}\n\n.expand_icon {\n  font-size: 24rpx;\n  color: #999;\n  margin-left: 20rpx; /* Add some spacing */\n  flex-shrink: 0; /* Prevent it from shrinking */\n}\n\n.service_items {\n  display: flex;\n  flex-wrap: wrap;\n  padding: 20rpx;\n}\n\n.service_item {\n  width: calc(33.33% - 20rpx); /* Adjusted for margin */\n  margin: 10rpx; /* Apply margin on all sides */\n  height: 80rpx;\n  background-color: #f5f5f5;\n  border-radius: 8rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 26rpx;\n  color: #666;\n  transition: all 0.3s;\n  text-align: center;\n  padding: 0 10rpx;\n  box-sizing: border-box; /* Include padding in width calculation */\n  white-space: nowrap; /* Prevent text wrapping */\n  overflow: hidden; /* Hide overflow text */\n  text-overflow: ellipsis; /* Show ellipsis for overflow */\n}\n\n.service_item.active {\n  background-color: #e6f0ff;\n  color: #2e80fe;\n  border: 1rpx solid #2e80fe;\n}\n\n.no-services,\n.no-content,\n.loading,\n.error {\n  text-align: center;\n  color: #999;\n  font-size: 28rpx;\n  padding: 40rpx;\n}\n\n.error {\n  color: #ff4d4f;\n}\n\n.footer {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 120rpx;\n  padding: 15rpx;\n  background-color: #fff;\n  border-top: 1rpx solid #f0f0f0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999; /* Increased z-index to ensure visibility */\n  box-sizing: border-box; /* Include padding in height calculation */\n}\n\n.save_btn {\n  width: 90%;\n  height: 90rpx;\n  background-color: #2e80fe;\n  color: white;\n  border-radius: 45rpx;\n  font-size: 32rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.save_btn.disabled {\n  background-color: #cccccc;\n  cursor: not-allowed;\n}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=style&index=0&id=6c7d5886&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./skillsIndex.vue?vue&type=style&index=0&id=6c7d5886&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755139157422\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}