{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/Promotioncommission.vue?9900", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/Promotioncommission.vue?7a4b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/Promotioncommission.vue?5e11", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/Promotioncommission.vue?b1f7", "uni-app:///user/Promotioncommission.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/Promotioncommission.vue?0323", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/Promotioncommission.vue?139f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list", "status", "page", "limit", "total", "loading", "onPullDownRefresh", "console", "uni", "title", "Promise", "icon", "onReachBottom", "onLoad", "methods", "getList", "pageNum", "pageSize"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,4BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4I;AAC5I;AACuE;AACL;AACsC;;;AAGxG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,0GAAM;AACR,EAAE,mHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAk2B,CAAgB,k3BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6Bt3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;IACAC;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAC,aACA,gBACA;MAAA;IAAA,GACA;MACAF;MACAA;IACA;MACAA;MACAA;QACAC;QACAE;MACA;MACAH;IACA;EACA;EACAI;IACA;IACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACAC;MACA;QACAV;QACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAC;UACAC;UACAE;QACA;QACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5GA;AAAA;AAAA;AAAA;AAAymD,CAAgB,6jDAAG,EAAC,C;;;;;;;;;;;ACA7nD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/Promotioncommission.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/Promotioncommission.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Promotioncommission.vue?vue&type=template&id=6f0911f8&scoped=true&\"\nvar renderjs\nimport script from \"./Promotioncommission.vue?vue&type=script&lang=js&\"\nexport * from \"./Promotioncommission.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Promotioncommission.vue?vue&type=style&index=0&id=6f0911f8&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f0911f8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/Promotioncommission.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Promotioncommission.vue?vue&type=template&id=6f0911f8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.$util.timestampToTime(item.createTime * 1000)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var g1 = !_vm.loading && _vm.list.length === 0\n  var g2 = _vm.loading && _vm.list.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Promotioncommission.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Promotioncommission.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"list_item\" v-for=\"(item, index) in list\" :key=\"index\">\n\t\t\t<view class=\"left\">\n\t\t\t\t<image src=\"../static/images/8957.png\" mode=\"\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"mid\">\n\t\t\t\t<view class=\"right\">{{ item.type === 1 ? \"推广\" : \"提现\" }}</view>\n\t\t\t\t<view class=\"time\">{{ $util.timestampToTime(item.createTime * 1000) }}</view>\n\t\t\t</view>\n\t\t\t<view class=\"right\">{{ item.type === 1 ? \"+\" : \"-\" }}{{ item.price }}</view>\n\t\t</view>\n\n\t\t<!-- 空数据状态 -->\n\t\t<view v-if=\"!loading && list.length === 0\" class=\"empty-state\">\n\t\t\t<image src=\"../static/images/empty.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n\t\t\t<text class=\"empty-text\">暂无推广佣金记录</text>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view v-if=\"loading && list.length === 0\" class=\"loading-state\">\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\n\t\t<!-- <u-loadmore :status=\"status\" /> -->\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tlist: [],\n\t\t\tstatus: 'loadmore',\n\t\t\tpage: 1,\n\t\t\tlimit: 15,\n\t\t\ttotal: 0, // Initialize total count\n\t\t\tloading: false // Prevent multiple simultaneous requests\n\t\t}\n\t},\n\tonPullDownRefresh() {\n\t\tconsole.log('refresh');\n\t\tuni.showLoading({\n\t\t\ttitle: '刷新中...'\n\t\t});\n\t\t// Reset state\n\t\tthis.page = 1;\n\t\tthis.list = [];\n\t\tthis.status = 'loadmore';\n\t\tthis.total = 0;\n\n\t\t// Fetch new data with minimum delay for better UX\n\t\tPromise.all([\n\t\t\tthis.getList(),\n\t\t\tnew Promise(resolve => setTimeout(resolve, 500))\n\t\t]).then(() => {\n\t\t\tuni.hideLoading();\n\t\t\tuni.stopPullDownRefresh();\n\t\t}).catch(() => {\n\t\t\tuni.hideLoading();\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '刷新失败，请重试',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tuni.stopPullDownRefresh();\n\t\t});\n\t},\n\tonReachBottom() {\n\t\t// Triggered when user scrolls to the bottom\n\t\tif (this.list.length < this.total && !this.loading) {\n\t\t\tthis.status = 'loading';\n\t\t\tthis.page++;\n\t\t\tthis.getList();\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.getList();\n\t},\n\tmethods: {\n\t\tgetList() {\n\t\t\tif (this.loading) return; // Prevent multiple requests\n\t\t\tthis.loading = true;\n\t\t\treturn this.$api.service.GETuserWater({\n\t\t\t\tpageNum: this.page,\n\t\t\t\tpageSize: this.limit\n\t\t\t}).then(ress => {\n\t\t\t\tconsole.log(ress);\n\t\t\t\tlet res = ress.data\n\t\t\t\tthis.total = res.totalCount; // Update total count from API response\n\t\t\t\t// Append new items to the list\n\t\t\t\tthis.list = this.page === 1 ? res.list : [...this.list, ...res.list];\n\t\t\t\tif (this.list.length >= this.total) {\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t} else {\n\t\t\t\t\tthis.status = 'loadmore';\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.status = 'nomore'; // Prevent further loading on error\n\t\t\t\tthrow err; // Re-throw to handle in refresh\n\t\t\t}).finally(() => {\n\t\t\t\tthis.loading = false;\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tpadding: 44rpx 30rpx;\n\n\t.list_item {\n\t\theight: 102rpx;\n\t\tdisplay: flex;\n\n\t\t.left {\n\t\t\twidth: 78rpx;\n\t\t\theight: 78rpx;\n\t\t\tborder-radius: 50%;\n\t\t\tbackground: #F9F9F9;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\n\t\t\timage {\n\t\t\t\twidth: 33rpx;\n\t\t\t\theight: 31rpx;\n\t\t\t}\n\t\t}\n\n\t\t.mid {\n\t\t\tmargin-left: 20rpx;\n\t\t\twidth: 520rpx;\n\n\t\t\t.name1 {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #171717;\n\t\t\t\tmax-width: 500rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\n\t\t\t.time {\n\t\t\t\tmargin-top: 12rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t}\n\t\t}\n\n\t\t.right {\n\t\t\twidth: 92rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tfont-weight: 500;\n\t\t\tcolor: #171717;\n\t\t}\n\t}\n\n\t// 空数据状态样式\n\t.empty-state {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 120rpx 0;\n\n\t\t.empty-image {\n\t\t\twidth: 200rpx;\n\t\t\theight: 200rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t}\n\n\t\t.empty-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999999;\n\t\t}\n\t}\n\n\t// 加载状态样式\n\t.loading-state {\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 120rpx 0;\n\n\t\t.loading-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999999;\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Promotioncommission.vue?vue&type=style&index=0&id=6f0911f8&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Promotioncommission.vue?vue&type=style&index=0&id=6f0911f8&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755137414815\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}