{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-update.vue?e6e0", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-update.vue?ea0a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-update.vue?492f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-update.vue?d291", "uni-app:///pages/debug-update.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-update.vue?2b5f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/debug-update.vue?89af"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "currentVersion", "platform", "systemInfo", "isLoading", "logs", "onLoad", "methods", "goBack", "uni", "initDebugInfo", "sysInfo", "appUpdate", "addLog", "message", "type", "console", "clearLogs", "testGetVersion", "version", "testApiCall", "$api", "response", "updateInfo", "testAppUpdateMethod", "silent", "showLoading", "result", "testAppLaunchMethod"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACuE/2B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACAC;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAGA;gBACAC;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;gBACA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;MACA;MACA;QACA;MACA;MACAC;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAN;cAAA;gBAAAO;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAF;kBACAjB;gBACA;cAAA;gBAHAoB;gBAKA;gBAEA;kBACAC;kBACA;oBACA;oBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAZ;kBACAa;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAGA;gBACA;;gBAEA;gBAAA;gBAAA,OACAhB;cAAA;gBAAAX;gBACA;;gBAEA;gBAAA;gBAAA,OACAoB;kBACAF;kBACAjB;gBACA;cAAA;gBAHAoB;gBAKA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;gBAEA;kBACA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBAAA;gBAAA,OACAX;kBAAAa;kBAAAC;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBAAA;gBAEA;gBAAA;gBAAA,OACAd;kBAAAa;kBAAAC;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAGA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7QA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/debug-update.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/debug-update.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./debug-update.vue?vue&type=template&id=64d774fc&scoped=true&\"\nvar renderjs\nimport script from \"./debug-update.vue?vue&type=script&lang=js&\"\nexport * from \"./debug-update.vue?vue&type=script&lang=js&\"\nimport style0 from \"./debug-update.vue?vue&type=style&index=0&id=64d774fc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"64d774fc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/debug-update.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-update.vue?vue&type=template&id=64d774fc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-update.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-update.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"debug-page\">\n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"nav-icon\">‹</text>\n      </view>\n      <view class=\"nav-title\">更新功能调试</view>\n      <view class=\"nav-right\"></view>\n    </view>\n\n    <!-- 基本信息 -->\n    <view class=\"info-section\">\n      <view class=\"info-title\">基本信息</view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">当前版本:</text>\n        <text class=\"info-value\">{{ currentVersion }}</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">平台类型:</text>\n        <text class=\"info-value\">{{ platform }} (师傅端)</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-label\">系统信息:</text>\n        <text class=\"info-value\">{{ systemInfo }}</text>\n      </view>\n    </view>\n\n    <!-- 测试按钮 -->\n    <view class=\"test-section\">\n      <view class=\"test-title\">功能测试</view>\n      \n      <button class=\"test-btn\" @click=\"testGetVersion\" :disabled=\"isLoading\">\n        测试获取版本号\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testApiCall\" :disabled=\"isLoading\">\n        测试API调用\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testAppUpdateMethod\" :disabled=\"isLoading\">\n        测试appUpdate.checkUpdate\n      </button>\n      \n      <button class=\"test-btn\" @click=\"testAppLaunchMethod\" :disabled=\"isLoading\">\n        测试App启动检查方法\n      </button>\n      \n      <button class=\"test-btn\" @click=\"clearLogs\">\n        清空日志\n      </button>\n    </view>\n\n    <!-- 日志显示 -->\n    <view class=\"log-section\">\n      <view class=\"log-title\">调试日志</view>\n      <view class=\"log-content\">\n        <text \n          class=\"log-item\" \n          v-for=\"(log, index) in logs\" \n          :key=\"index\"\n          :class=\"log.type\"\n        >\n          {{ log.message }}\n        </text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport appUpdate from '@/utils/app-update.js'\nimport $api from '@/api/index.js'\n\nexport default {\n  name: 'DebugUpdate',\n  data() {\n    return {\n      currentVersion: '未知',\n      platform: 1,\n      systemInfo: '获取中...',\n      isLoading: false,\n      logs: []\n    }\n  },\n  onLoad() {\n    this.initDebugInfo()\n  },\n  methods: {\n    /**\n     * 返回上一页\n     */\n    goBack() {\n      uni.navigateBack()\n    },\n\n    /**\n     * 初始化调试信息\n     */\n    async initDebugInfo() {\n      this.addLog('=== 开始初始化调试信息 ===', 'info')\n      \n      try {\n        // 获取系统信息\n        const sysInfo = uni.getSystemInfoSync()\n        this.systemInfo = `${sysInfo.platform} ${sysInfo.appVersion || '未知'}`\n        this.addLog(`系统信息: ${JSON.stringify(sysInfo)}`, 'info')\n        \n        // 获取当前版本\n        this.currentVersion = await appUpdate.getCurrentVersion()\n        this.addLog(`当前版本: ${this.currentVersion}`, 'info')\n        \n        this.addLog('=== 初始化完成 ===', 'success')\n      } catch (error) {\n        this.addLog(`初始化失败: ${error.message}`, 'error')\n      }\n    },\n\n    /**\n     * 添加日志\n     */\n    addLog(message, type = 'info') {\n      const time = new Date().toLocaleTimeString()\n      this.logs.unshift({\n        message: `[${time}] ${message}`,\n        type: type\n      })\n      if (this.logs.length > 100) {\n        this.logs = this.logs.slice(0, 100)\n      }\n      console.log(`[DEBUG] ${message}`)\n    },\n\n    /**\n     * 清空日志\n     */\n    clearLogs() {\n      this.logs = []\n    },\n\n    /**\n     * 测试获取版本号\n     */\n    async testGetVersion() {\n      this.isLoading = true\n      this.addLog('=== 测试获取版本号 ===', 'info')\n      \n      try {\n        const version = await appUpdate.getCurrentVersion()\n        this.addLog(`获取版本成功: ${version}`, 'success')\n        this.currentVersion = version\n      } catch (error) {\n        this.addLog(`获取版本失败: ${error.message}`, 'error')\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    /**\n     * 测试API调用\n     */\n    async testApiCall() {\n      this.isLoading = true\n      this.addLog('=== 测试API调用 ===', 'info')\n      \n      try {\n        const response = await $api.user.checkAppVersion({\n          version: this.currentVersion,\n          platform: this.platform\n        })\n        \n        this.addLog(`API调用成功: ${JSON.stringify(response)}`, 'success')\n        \n        if (response.code === '200' && response.data) {\n          const updateInfo = response.data\n          if (updateInfo.needUpdate) {\n            this.addLog(`发现新版本: v${updateInfo.latestVersion}`, 'info')\n            this.addLog(`更新描述: ${updateInfo.description}`, 'info')\n            this.addLog(`是否强制更新: ${updateInfo.forceUpdate}`, 'info')\n          } else {\n            this.addLog('已是最新版本', 'info')\n          }\n        } else {\n          this.addLog(`API返回错误: ${response.msg}`, 'warning')\n        }\n      } catch (error) {\n        this.addLog(`API调用失败: ${error.message}`, 'error')\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    /**\n     * 测试appUpdate.checkUpdate方法\n     */\n    async testAppUpdateMethod() {\n      this.isLoading = true\n      this.addLog('=== 测试appUpdate.checkUpdate方法 ===', 'info')\n      \n      try {\n        const result = await appUpdate.checkUpdate({\n          silent: true,\n          showLoading: false\n        })\n        \n        if (result) {\n          this.addLog(`检查更新成功，有新版本: ${JSON.stringify(result)}`, 'success')\n        } else {\n          this.addLog('检查更新成功，已是最新版本', 'success')\n        }\n      } catch (error) {\n        this.addLog(`检查更新失败: ${error.message}`, 'error')\n      } finally {\n        this.isLoading = false\n      }\n    },\n\n    /**\n     * 测试App启动检查方法\n     */\n    async testAppLaunchMethod() {\n      this.isLoading = true\n      this.addLog('=== 测试App启动检查方法 ===', 'info')\n      \n      try {\n        // 模拟App.vue中的checkAppUpdateOnLaunch方法\n        this.addLog('=== 开始检查APP更新 ===', 'info')\n\n        // 获取当前版本\n        const currentVersion = await appUpdate.getCurrentVersion()\n        this.addLog(`当前版本: ${currentVersion}`, 'info')\n\n        // 调用后端接口检查更新\n        const response = await $api.user.checkAppVersion({\n          version: currentVersion,\n          platform: 1 // 师傅端\n        })\n\n        this.addLog(`版本检查响应: ${JSON.stringify(response)}`, 'info')\n\n        if (response.code === '200' && response.data) {\n          const updateInfo = response.data\n          this.addLog(`更新信息: ${JSON.stringify(updateInfo)}`, 'info')\n\n          if (updateInfo.needUpdate) {\n            this.addLog('=== 发现新版本，显示更新提醒 ===', 'success')\n            // 这里不实际显示弹窗，只记录日志\n            this.addLog('模拟显示更新对话框', 'info')\n          } else {\n            this.addLog('=== 已是最新版本，无需更新 ===', 'success')\n          }\n        } else {\n          this.addLog(`检查更新失败: ${response.msg || '未知错误'}`, 'warning')\n          this.addLog('尝试使用默认更新检查方法...', 'info')\n          await appUpdate.checkUpdate({ silent: true, showLoading: false })\n        }\n      } catch (error) {\n        this.addLog(`检查更新异常: ${error.message}`, 'error')\n        try {\n          this.addLog('异常后尝试使用默认更新检查方法...', 'info')\n          await appUpdate.checkUpdate({ silent: true, showLoading: false })\n        } catch (fallbackError) {\n          this.addLog(`默认更新检查也失败: ${fallbackError.message}`, 'error')\n        }\n      } finally {\n        this.isLoading = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.debug-page {\n  min-height: 100vh;\n  background: #f5f5f5;\n}\n\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 24rpx;\n  background: #fff;\n  border-bottom: 1rpx solid #eee;\n  \n  .nav-left, .nav-right {\n    width: 80rpx;\n  }\n  \n  .nav-icon {\n    font-size: 36rpx;\n    color: #333;\n    font-weight: bold;\n  }\n  \n  .nav-title {\n    font-size: 32rpx;\n    color: #333;\n    font-weight: 500;\n  }\n}\n\n.info-section, .test-section {\n  background: #fff;\n  margin: 20rpx;\n  border-radius: 12rpx;\n  padding: 32rpx 24rpx;\n}\n\n.info-title, .test-title {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20rpx;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 16rpx;\n  \n  .info-label {\n    font-size: 28rpx;\n    color: #666;\n  }\n  \n  .info-value {\n    font-size: 28rpx;\n    color: #333;\n    flex: 1;\n    text-align: right;\n    word-break: break-all;\n  }\n}\n\n.test-btn {\n  width: 100%;\n  height: 80rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 30rpx;\n  font-weight: 500;\n  margin-bottom: 16rpx;\n  \n  &:disabled {\n    background: #ccc;\n  }\n  \n  &:active:not(:disabled) {\n    opacity: 0.8;\n  }\n}\n\n.log-section {\n  background: #fff;\n  margin: 0 20rpx 20rpx;\n  border-radius: 12rpx;\n  padding: 32rpx 24rpx;\n}\n\n.log-title {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 600;\n  margin-bottom: 20rpx;\n}\n\n.log-content {\n  max-height: 800rpx;\n  overflow-y: auto;\n  background: #f8f8f8;\n  border-radius: 8rpx;\n  padding: 20rpx;\n}\n\n.log-item {\n  display: block;\n  font-size: 24rpx;\n  line-height: 1.6;\n  margin-bottom: 8rpx;\n  word-break: break-all;\n  \n  &.info {\n    color: #666;\n  }\n  \n  &.success {\n    color: #52c41a;\n  }\n  \n  &.warning {\n    color: #faad14;\n  }\n  \n  &.error {\n    color: #f5222d;\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-update.vue?vue&type=style&index=0&id=64d774fc&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./debug-update.vue?vue&type=style&index=0&id=64d774fc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755133982372\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}