{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coreWallet.vue?6e19", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coreWallet.vue?2b78", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coreWallet.vue?87cc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coreWallet.vue?9079", "uni-app:///user/coreWallet.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coreWallet.vue?e62b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coreWallet.vue?b99a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "totalAmount", "currentStatus", "recordList", "loading", "hasMore", "pageNum", "pageSize", "statusOptions", "label", "value", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "initData", "refreshData", "uni", "getTotalAmount", "res", "console", "getRecordList", "reset", "params", "status", "type", "title", "icon", "loadMore", "changeStatus", "handleRecordTap", "handleStatusTap", "item", "viewDetail", "url", "processWithdrawal", "content", "confirmText", "cancelText", "success", "processCancel", "<PERSON><PERSON><PERSON><PERSON>wal", "id", "mchId", "appId", "package", "setTimeout", "fail", "complete", "showCancel", "executeCancel", "getCashToTypeText", "getStatusClass"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgD72B;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC,gBACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGAC;kBACAC;kBACAC;kBAAA;kBACAnB;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAY;gBAEA;kBACAnB;kBACA;oBACA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;kBACA;oBACA;kBACA;gBACA;kBACAiB;oBACAS;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAH;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACAhB;QACAiB;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAlB;kBACAS;kBACAU;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACApB;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAqB;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAvB;kBACAS;kBACAU;kBACAC;kBACAC;kBACAC;oBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACApB;gCAAA;gCAAA;8BAAA;8BAAA;8BAAA,OACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACA;;kBAEA;gBAAA;gBA0BAxB;kBAAAS;gBAAA;gBAAA;gBAAA;gBAAA,OAGA;kBAAAgB;gBAAA;cAAA;gBAAAvB;gBAEA;kBACA;kBACA;oBACAC;oBACA;oBACA;sBACA;sBACAH;wBACA0B;wBACAC;wBACAC;wBACAN;0BACAtB;4BACAS;4BACAC;0BACA;0BACAmB;4BACA;0BACA;wBACA;wBACAC;0BACA9B;4BACAS;4BACAC;0BACA;wBACA;wBACAqB;0BACA/B;wBACA;sBACA;oBACA;sBACAA;sBACAA;wBACAS;wBACAU;wBACAa;sBACA;oBACA;kBACA;oBACA;oBACAhC;sBACAS;sBACAC;oBACA;oBACAmB;sBACA;oBACA;kBACA;gBACA;kBACA7B;oBACAS;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAH;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAEAV;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAGA;IACAiC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjC;kBAAAS;gBAAA;gBAAA;gBAEAH;kBACAmB;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAvB;gBAAA;gBACA;kBACAF;oBACAS;oBACAC;kBACA;kBACA;gBACA;kBACAV;oBACAS;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAH;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAEAV;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QAAA;QACA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxYA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/coreWallet.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/coreWallet.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coreWallet.vue?vue&type=template&id=342423c4&scoped=true&\"\nvar renderjs\nimport script from \"./coreWallet.vue?vue&type=script&lang=js&\"\nexport * from \"./coreWallet.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coreWallet.vue?vue&type=style&index=0&id=342423c4&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"342423c4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/coreWallet.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=template&id=342423c4&scoped=true&\"", "var components\ntry {\n  components = {\n    uScrollList: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-scroll-list/u-scroll-list\" */ \"uview-ui/components/u-scroll-list/u-scroll-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.recordList, function (item, __i1__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getStatusClass(item)\n    var m1 = _vm.getCashToTypeText(item.cashToType)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g0 = _vm.recordList.length === 0 && !_vm.loading\n  var g1 = _vm.recordList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header-stats\">\n\t\t\t<view class=\"stats-card\">\n\t\t\t\t<view class=\"stats-title\">已到账总额</view>\n\t\t\t\t<view class=\"stats-amount\">￥{{ totalAmount }}</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"filter-section\">\n\t\t\t<u-scroll-list :indicator=\"true\" indicator-color=\"#f2f2f2\" indicator-active-color=\"#2e80fe\">\n\t\t\t\t<view class=\"filter-tab\" :class=\"{ active: currentStatus === item.value }\" v-for=\"item in statusOptions\"\n\t\t\t\t\t:key=\"item.value\" @tap=\"changeStatus(item.value)\">\n\t\t\t\t\t{{ item.label }}\n\t\t\t\t</view>\n\t\t\t</u-scroll-list>\n\t\t</view>\n\t\t<view class=\"record-list\">\n\t\t\t<view class=\"record-item\" v-for=\"item in recordList\" :key=\"item.id\" @tap=\"handleRecordTap(item)\">\n\t\t\t\t<view class=\"record-header\">\n\t\t\t\t\t<view class=\"record-amount\">￥{{ item.amount }}</view>\n\t\t\t\t\t<view class=\"record-status\" :class=\"[getStatusClass(item)]\" @tap.stop=\"handleStatusTap(item)\">\n\t\t\t\t\t\t{{ item.statusText }}\n\t\t\t\t\t\t<text v-if=\"item.lock === 1\" class=\"tap-hint\">点击提现</text>\n\t\t\t\t\t\t<text v-if=\"item.lock === 0\" class=\"tap-hint\">点击取消</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t<view class=\"record-time\">{{ item.createTime }}</view>\n\t\t\t\t\t<view class=\"record-method\">提现到：{{ getCashToTypeText(item.cashToType) }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"empty-state\" v-if=\"recordList.length === 0 && !loading\">\n\t\t\t<!-- <image src=\"/static/mine/default_user.png\" class=\"empty-image\"></image> -->\n\t\t\t<text class=\"empty-text\">暂无提现记录</text>\n\t\t</view>\n\n\t\t<view class=\"load-more\" v-if=\"recordList.length > 0\">\n\t\t\t<text class=\"load-text\" v-if=\"hasMore && !loading\">上拉加载更多</text>\n\t\t\t<text class=\"load-text\" v-if=\"loading\">加载中...</text>\n\t\t\t<text class=\"load-text\" v-if=\"!hasMore && !loading\">没有更多数据了</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\ttotalAmount: 0, // 已到账总额\n\t\t\tcurrentStatus: '', // 当前选中的状态\n\t\t\trecordList: [], // 提现记录列表\n\t\t\tloading: false, // 加载状态\n\t\t\thasMore: true, // 是否还有更多数据\n\t\t\tpageNum: 1, // 当前页码\n\t\t\tpageSize: 10, // 每页数量\n\t\t\tstatusOptions: [\n\t\t\t\t{ label: '全部', value: '' },\n\t\t\t\t{ label: '未提现', value: 0 },\n\t\t\t\t{ label: '已提现未领取', value: 1 },\n\t\t\t\t{ label: '已到账', value: 2 },\n\t\t\t\t{ label: '失败', value: 3 },\n\t\t\t\t{ label: '关闭', value: 4 }\n\t\t\t]\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.initData();\n\t},\n\tonPullDownRefresh() {\n\t\tthis.refreshData();\n\t},\n\tonReachBottom() {\n\t\tthis.loadMore();\n\t},\n\tmethods: {\n\t\t// 初始化数据\n\t\tasync initData() {\n\t\t\tawait this.getTotalAmount();\n\t\t\tawait this.getRecordList(true);\n\t\t},\n\n\t\t// 刷新数据\n\t\tasync refreshData() {\n\t\t\tthis.pageNum = 1;\n\t\t\tthis.hasMore = true;\n\t\t\tawait this.getTotalAmount();\n\t\t\tawait this.getRecordList(true);\n\t\t\tuni.stopPullDownRefresh();\n\t\t},\n\n\t\t// 获取已到账总额\n\t\tasync getTotalAmount() {\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.mine.walletStatSimple();\n\t\t\t\tif (res.code === '200') {\n\t\t\t\t\tthis.totalAmount = res.data || 0;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取总额失败:', error);\n\t\t\t}\n\t\t},\n\n\t\t// 获取提现记录列表\n\t\tasync getRecordList(reset = false) {\n\t\t\tif (this.loading) return;\n\n\t\t\tthis.loading = true;\n\n\t\t\ttry {\n\t\t\t\tconst params = {\n\t\t\t\t\tstatus: this.currentStatus === '' ? '' : this.currentStatus,\n\t\t\t\t\ttype: 4, // 服务费\n\t\t\t\t\tpageNum: reset ? 1 : this.pageNum,\n\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t};\n\n\t\t\t\tconst res = await this.$api.mine.walletList(params);\n\n\t\t\t\tif (res.code === '200') {\n\t\t\t\t\tconst data = res.data;\n\t\t\t\t\tif (reset) {\n\t\t\t\t\t\tthis.recordList = data.list || [];\n\t\t\t\t\t\tthis.pageNum = 1;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.recordList = [...this.recordList, ...(data.list || [])];\n\t\t\t\t\t}\n\n\t\t\t\t\t// 判断是否还有更多数据\n\t\t\t\t\tthis.hasMore = this.pageNum < data.totalPage;\n\t\t\t\t\tif (!reset) {\n\t\t\t\t\t\tthis.pageNum++;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '获取数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取记录失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\n\t\t// 加载更多\n\t\tloadMore() {\n\t\t\tif (this.hasMore && !this.loading) {\n\t\t\t\tthis.getRecordList();\n\t\t\t}\n\t\t},\n\n\t\t// 切换状态筛选\n\t\tchangeStatus(status) {\n\t\t\tif (this.currentStatus === status) return;\n\n\t\t\tthis.currentStatus = status;\n\t\t\tthis.pageNum = 1;\n\t\t\tthis.hasMore = true;\n\t\t\tthis.getRecordList(true);\n\t\t},\n\n\t\t// 处理记录点击 (Always navigate to detail)\n\t\thandleRecordTap(item) {\n\t\t\tthis.viewDetail(item.id);\n\t\t},\n\n\t\t// 处理状态点击 (Handle specific actions)\n\t\tasync handleStatusTap(item) {\n\t\t\tif (item.lock === 1) { // 待提现 或 已提现，未领取\n\t\t\t\tawait this.processWithdrawal(item);\n\t\t\t} else if (item.lock === 0) { // 待审核\n\t\t\t\tawait this.processCancel(item);\n\t\t\t} else {\n\t\t\t\t// If lock is not 0 or 1, clicking status also views detail\n\t\t\t\tthis.viewDetail(item.id);\n\t\t\t}\n\t\t},\n\n\t\t// 查看详情\n\t\tviewDetail(id) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/user/walletDetail?id=${id}`\n\t\t\t});\n\t\t},\n\n\t\t// 处理提现请求\n\t\tasync processWithdrawal(item) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认提现',\n\t\t\t\tcontent: `确认提现 ￥${item.amount} 吗？`,\n\t\t\t\tconfirmText: '确认',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.executeWithdrawal(item);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 处理取消提现请求 (for '待审核' status)\n\t\tasync processCancel(item) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '取消提现',\n\t\t\t\tcontent: `确认取消提现申请吗？`,\n\t\t\t\tconfirmText: '确认',\n\t\t\t\tcancelText: '取消',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tawait this.executeCancel(item);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 执行提现请求\n\t\tasync executeWithdrawal(item) {\n\t\t\t// 如果是微信提现，检查运行环境\n\t\t\tif (item.cashToType === 1) {\n\t\t\t\t// 检查是否在微信小程序环境\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t// 在微信小程序中，可以直接处理微信提现\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t// 在APP中，提醒用户去微信小程序操作\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '微信提现需要在微信小程序中操作，请前往微信小程序进行提现',\n\t\t\t\t\tconfirmText: '知道了',\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifndef MP-WEIXIN || APP-PLUS\n\t\t\t\t// 其他环境（如H5），也提醒用户去微信小程序\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '微信提现需要在微信小程序中操作，请前往微信小程序进行提现',\n\t\t\t\t\tconfirmText: '知道了',\n\t\t\t\t\tshowCancel: false\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t\t// #endif\n\t\t\t}\n\n\t\t\tuni.showLoading({ title: '提现中...' });\n\n\t\t\ttry {\n\t\t\t\tconst res = await this.$api.mine.withdrawalRequest({ id: item.id });\n\n\t\t\t\tif (res.code === '200') {\n\t\t\t\t\t// 微信提现处理\n\t\t\t\t\tif (item.cashToType === 1) {\n\t\t\t\t\t\tconsole.log('uni.requestMerchantTransfer:', typeof uni.requestMerchantTransfer);\n\t\t\t\t\t\t// 检查是否支持 requestMerchantTransfer API\n\t\t\t\t\t\tif (typeof uni.requestMerchantTransfer === 'function') {\n\t\t\t\t\t\t\t// 拉起微信收款确认弹窗\n\t\t\t\t\t\t\tuni.requestMerchantTransfer({\n\t\t\t\t\t\t\t\tmchId: res.data.mchId,\n\t\t\t\t\t\t\t\tappId: res.data.appId,\n\t\t\t\t\t\t\t\tpackage: res.data.packageInfo,\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '提现成功',\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: err.errMsg || '提现失败，请稍后重试',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tcomplete: () => {\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\tcontent: '当前环境暂不支持微信提现功能',\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 支付宝或银行卡提现处理\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '提现申请已提交',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.refreshData();\n\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '提现请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('提现请求失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t}\n\t\t,\n\n\t\t// 执行取消提现请求\n\t\tasync executeCancel(item) {\n\t\t\tuni.showLoading({ title: '取消中...' });\n\t\t\ttry {\n\t\t\t\tconst params = {\n\t\t\t\t\tid: item.id // Pass the withdrawal record ID\n\t\t\t\t};\n\t\t\t\tconst res = await this.$api.mine.cancelStatSimple(item.id); // Assuming this is the correct API call\n\t\t\t\tif (res.code === '200') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '提现申请已取消',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\tthis.refreshData(); // Refresh the list after successful cancellation\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.msg || '取消提现失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('取消提现失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请稍后重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\n\t\t// 获取提现方式文本\n\t\tgetCashToTypeText(cashToType) {\n\t\t\tconst typeMap = {\n\t\t\t\t1: '微信',\n\t\t\t\t2: '支付宝',\n\t\t\t\t3: '银行卡'\n\t\t\t};\n\t\t\treturn typeMap[cashToType] || '未知';\n\t\t},\n\n\t\t// 获取状态样式类\n\t\tgetStatusClass(item) {\n\t\t\tconst statusMap = {\n\t\t\t\t'审核拒绝': 'status-rejected',\n\t\t\t\t'待提现': 'status-withdraw', // Will be overridden by lock for styling\n\t\t\t\t'已提现，未领取': 'status-withdraw', // Will be overridden by lock for styling\n\t\t\t\t'已提现': 'status-processing',\n\t\t\t\t'提现成功': 'status-success',\n\t\t\t\t'提现失败': 'status-failed',\n\t\t\t\t'已到账': 'status-success',\n\t\t\t\t'失败': 'status-failed',\n\t\t\t\t'关闭': 'status-closed',\n\t\t\t\t'待审核': 'status-pending' // Will be overridden by lock for styling\n\t\t\t};\n\n\t\t\t// Apply styling based on 'lock' field\n\t\t\tif (item.lock === 1) {\n\t\t\t\treturn 'status-actionable'; // New class for actionable items (待提现, 已提现，未领取)\n\t\t\t} else if (item.lock === 0) {\n\t\t\t\treturn 'status-pending-action'; // New class for '待审核' when user can cancel\n\t\t\t}\n\n\t\t\treturn statusMap[item.statusText] || 'status-default';\n\t\t}\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tbackground-color: #f0f2f5;\n\tmin-height: 100vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.header-stats {\n\tbackground: linear-gradient(135deg, #2e80fe 0%, #1e6bff 100%);\n\tpadding: 60rpx 30rpx 80rpx;\n\tborder-bottom-left-radius: 40rpx;\n\tborder-bottom-right-radius: 40rpx;\n\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);\n\n\t.stats-card {\n\t\ttext-align: center;\n\t\tcolor: #ffffff;\n\n\t\t.stats-title {\n\t\t\tfont-size: 30rpx;\n\t\t\topacity: 0.95;\n\t\t\tmargin-bottom: 24rpx;\n\t\t\tfont-weight: 400;\n\t\t}\n\n\t\t.stats-amount {\n\t\t\tfont-size: 56rpx;\n\t\t\tfont-weight: bold;\n\t\t\tletter-spacing: 1rpx;\n\t\t}\n\t}\n}\n\n.filter-section {\n\tbackground: #ffffff;\n\tmargin: -40rpx 30rpx 30rpx;\n\tborder-radius: 20rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\tpadding: 24rpx 0;\n\t/* Remove position: relative and overflow: hidden if u-scroll-list handles its own clipping/gradients */\n\n\t/* If using u-scroll-list, the .filter-tabs-container and its scrollbar hiding are handled by u-scroll-list internally.\n\t   You might still need to style the inner items. */\n\n\t/* Remove these custom gradient overlays if using a dedicated ScrollList component */\n\t/*\n\t&::before,\n\t&::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tbottom: 0;\n\t\twidth: 60rpx;\n\t\tpointer-events: none;\n\t\tz-index: 1;\n\t}\n\n\t&::before {\n\t\tleft: 0;\n\t\tbackground: linear-gradient(to right, #ffffff 30%, rgba(255, 255, 255, 0) 100%);\n\t}\n\n\t&::after {\n\t\tright: 0;\n\t\tbackground: linear-gradient(to left, #ffffff 30%, rgba(255, 255, 255, 0) 100%);\n\t}\n\t*/\n\n\n\t/* Styling for the individual tabs within the scroll list */\n\t.filter-tab {\n\t\tpadding: 18rpx 30rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tborder-radius: 24rpx;\n\t\ttransition: all 0.3s ease;\n\t\ttext-align: center;\n\t\tflex-shrink: 0;\n\t\t/* Prevent tabs from shrinking */\n\t\tmargin: 0 10rpx;\n\t\t/* Add margin between tabs for better spacing, adjust as needed */\n\n\t\t&:first-child {\n\t\t\tmargin-left: 30rpx;\n\t\t\t/* Align first tab with filter-section padding */\n\t\t}\n\n\t\t&:last-child {\n\t\t\tmargin-right: 30rpx;\n\t\t\t/* Align last tab with filter-section padding */\n\t\t}\n\n\t\t&.active {\n\t\t\tcolor: #2e80fe;\n\t\t\tbackground: rgba(46, 128, 254, 0.15);\n\t\t\tfont-weight: 600;\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(46, 128, 254, 0.2);\n\t\t}\n\t}\n\n\t// The u-scroll-list component itself might have padding, so you may need to adjust the padding of .filter-section\n\t// or the margin of .filter-tab to ensure proper alignment and prevent double padding.\n}\n\n.record-list {\n\tpadding: 0 30rpx;\n\tflex-grow: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\t/* Centers each record-item horizontally */\n\n\t.record-item {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 30rpx 40rpx;\n\t\tmargin-bottom: 24rpx;\n\t\tbox-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.07);\n\t\ttransition: transform 0.2s ease-in-out;\n\t\twidth: 100%;\n\t\t/* Take full width within padding */\n\t\tmax-width: 700rpx;\n\t\t/* Optional: Constrain max width for large screens */\n\n\t\t&:active {\n\t\t\ttransform: translateY(2rpx);\n\t\t}\n\n\t\t.record-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t.record-amount {\n\t\t\t\tfont-size: 38rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #333333;\n\t\t\t}\n\n\t\t\t.record-status {\n\t\t\t\tpadding: 10rpx 20rpx;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tposition: relative;\n\t\t\t\tcursor: pointer;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\twhite-space: nowrap;\n\n\t\t\t\t&.status-rejected {\n\t\t\t\t\tbackground: #fde0e0;\n\t\t\t\t\tcolor: #d32f2f;\n\t\t\t\t}\n\n\t\t\t\t&.status-pending {\n\t\t\t\t\tbackground: #fff8e1;\n\t\t\t\t\tcolor: #f57c00;\n\t\t\t\t}\n\n\t\t\t\t&.status-actionable {\n\t\t\t\t\tbackground: #e1f5fe;\n\t\t\t\t\tcolor: #039be5;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground: #bbdefb;\n\t\t\t\t\t}\n\n\t\t\t\t\t.tap-hint {\n\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\t\topacity: 0.85;\n\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.status-pending-action {\n\t\t\t\t\tbackground: #fff8e1;\n\t\t\t\t\tcolor: #f57c00;\n\n\t\t\t\t\t&:active {\n\t\t\t\t\t\tbackground: #ffe0b2;\n\t\t\t\t\t}\n\n\t\t\t\t\t.tap-hint {\n\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\t\topacity: 0.85;\n\t\t\t\t\t\tcolor: inherit;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t&.status-processing {\n\t\t\t\t\tbackground: #e3f2fd;\n\t\t\t\t\tcolor: #2196f3;\n\t\t\t\t}\n\n\t\t\t\t&.status-success {\n\t\t\t\t\tbackground: #e8f5e8;\n\t\t\t\t\tcolor: #4caf50;\n\t\t\t\t}\n\n\t\t\t\t&.status-failed {\n\t\t\t\t\tbackground: #ffebee;\n\t\t\t\t\tcolor: #f44336;\n\t\t\t\t}\n\n\t\t\t\t&.status-closed {\n\t\t\t\t\tbackground: #f5f5f5;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\n\t\t\t\t&.status-default {\n\t\t\t\t\tbackground: #f0f0f0;\n\t\t\t\t\tcolor: #666666;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.record-info {\n\t\t\t.record-time {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #888888;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t}\n\n\t\t\t.record-method {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #666666;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 150rpx 0;\n\tflex-grow: 1;\n\n\t.empty-image {\n\t\twidth: 240rpx;\n\t\theight: 240rpx;\n\t\tmargin-bottom: 40rpx;\n\t\topacity: 0.7;\n\t}\n\n\t.empty-text {\n\t\tfont-size: 30rpx;\n\t\tcolor: #999999;\n\t\tfont-weight: 500;\n\t\tmargin-bottom: 400rpx;\n\t}\n}\n\n.load-more {\n\tpadding: 40rpx 0 60rpx;\n\ttext-align: center;\n\n\t.load-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #999999;\n\t}\n}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=style&index=0&id=342423c4&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coreWallet.vue?vue&type=style&index=0&id=342423c4&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755139172953\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}