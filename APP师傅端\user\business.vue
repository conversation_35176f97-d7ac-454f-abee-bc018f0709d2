<template>
	<view class="page">
		<view class="header">邀请好友 赚奖励</view>
		<view class="box">
			<view class="name">{{ info.name || '' }}</view>
			<view class="desc">每邀请一位师傅和商家入驻，师傅、商家订单验收完结后，邀请人每单可获得1%的奖励。</view>
			<image :src="imgUrl" mode=""></image>
			<view class="invite-code-container">
				<!-- <view class="invite-code">邀请码: {{ inviteCode|| '无'}}</view> -->
			</view>
		</view>
		<!-- #ifdef MP-WEIXIN -->
		<view class="button-container">
			<view class="btn save-btn" @click="saveImageWithPermission">保存图片</view>
			<button class="btn share-btn" open-type="share" :disabled="!imgUrl">分享</button>
		</view>
		<!-- #endif -->

		<!-- #ifdef APP-PLUS -->
		<view class="button-container app-single-btn">
			<view class="btn save-btn" @click="saveImageWithPermission">保存图片</view>
		</view>
		<!-- #endif -->
	</view>
</template>

<script>
export default {
	data() {
		return {
			imgUrl: '',
			infos: '',
			inviteCode: '',
			info: {}
		}
	},
	created() {
		//#ifdef MP-WEIXIN
		wx.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline'],
			success: () => {
				console.log('Share menu enabled');
			},
			fail: (e) => {
				console.error('Failed to enable share menu:', e);
			}
		});
		//#endif
	},
	onShareAppMessage(res) {
		const inviteCode = this.inviteCode || '';
		const shareData = {
			title: '邀好友，赚现金.邀请师傅和商家，接单下单都赚钱',
			path: `/pages/mine?inviteCode=${inviteCode}`,
			imageUrl: this.imgUrl || ''
		};
		console.log('Sharing with:', shareData);
		return shareData;
	},
	methods: {
		// 带权限检查的保存图片方法
		saveImageWithPermission() {
			if (!this.imgUrl) {
				uni.showToast({
					title: '图片未加载',
					icon: 'none'
				});
				return;
			}

			// #ifdef MP-WEIXIN
			// 微信小程序需要检查授权状态
			this.checkAuthStatus();
			// #endif

			// #ifdef APP-PLUS
			// APP版本直接尝试保存图片
			this.saveImageForApp();
			// #endif
		},

		// 检查用户授权状态
		checkAuthStatus() {
			const that = this;

			uni.getSetting({
				success: function (res) {
					if (res.authSetting['scope.writePhotosAlbum']) {
						// 已授权，直接开始下载保存
						that.downloadAndSaveImage();
					} else if (res.authSetting['scope.writePhotosAlbum'] === undefined) {
						// 首次请求，还未授权过，直接开始下载（会自动弹出授权）
						that.downloadAndSaveImage();
					} else {
						// 用户曾经拒绝过授权，需要引导用户手动开启
						that.requestPermission();
					}
				},
				fail: function (error) {
					console.log(error, 'getSetting');
					uni.showToast({
						title: '获取授权状态失败',
						icon: 'none'
					});
				}
			});
		},

		// 请求用户授权
		requestPermission() {
			const that = this;

			uni.showModal({
				title: '授权提示',
				content: '需要您授权保存图片到相册，请在设置中开启相册权限',
				showCancel: true,
				confirmText: '去设置',
				cancelText: '取消',
				success: function (resShow) {
					if (resShow.confirm) {
						// 用户点击去设置，打开设置页面
						uni.openSetting({
							success: function (resOpen) {
								// 用户在设置页面操作完成后，重新检查权限
								if (resOpen.authSetting['scope.writePhotosAlbum']) {
									that.downloadAndSaveImage();
								} else {
									uni.showToast({
										title: '未开启相册权限',
										icon: 'none'
									});
								}
							},
							fail: function (errorOpen) {
								console.log(errorOpen, 'openSetting');
								uni.showToast({
									title: '打开设置失败',
									icon: 'none'
								});
							}
						});
					} else {
						// 用户取消授权
						uni.showToast({
							title: '取消授权无法保存',
							icon: 'none'
						});
					}
				}
			});
		},

		// 下载并保存图片
		downloadAndSaveImage() {
			const that = this;

			// 显示加载提示
			uni.showLoading({
				title: '保存中...',
				mask: true
			});

			uni.downloadFile({
				url: this.imgUrl,
				// 生成唯一的文件名
				filePath: uni.env.USER_DATA_PATH + '/invite_' + new Date().getTime() + '.jpg',
				success(res) {
					if (res.statusCode === 200) {
						// 下载成功，保存到相册
						that.saveToAlbum(res.filePath);
					} else {
						uni.hideLoading();
						console.error('Download failed with status:', res.statusCode);
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				},
				fail(error) {
					uni.hideLoading();
					console.error('Download error:', error);

					// 根据错误类型给出不同提示
					let errorMsg = '图片下载失败';
					if (error.errMsg) {
						if (error.errMsg.includes('network')) {
							errorMsg = '网络连接失败';
						} else if (error.errMsg.includes('timeout')) {
							errorMsg = '下载超时';
						}
					}

					uni.showToast({
						title: errorMsg,
						icon: 'none'
					});
				}
			});
		},

		// 保存图片到系统相册
		saveToAlbum(filePath) {
			const that = this;

			uni.saveImageToPhotosAlbum({
				filePath: filePath,
				success: function () {
					uni.hideLoading();
					console.log('图片保存成功');
					uni.showToast({
						title: '保存成功',
						icon: 'success',
						duration: 2000
					});

					// 可以在这里添加保存成功后的业务逻辑
					// 比如记录下载次数、统计等
					that.recordSaveAction();
				},
				fail: function (error) {
					uni.hideLoading();
					console.error('Save image failed:', error);

					// 处理不同的保存失败情况
					let errorMsg = '保存失败';
					if (error.errMsg) {
						if (error.errMsg.includes('auth deny')) {
							errorMsg = '保存失败，请检查相册权限';
							// 如果是权限问题，可以再次引导用户授权
							setTimeout(() => {
								that.requestPermission();
							}, 1500);
						} else if (error.errMsg.includes('system deny')) {
							errorMsg = '系统拒绝保存';
						}
					}

					uni.showToast({
						title: errorMsg,
						icon: 'none'
					});
				}
			});
		},

		// APP版本保存图片方法
		saveImageForApp() {
			const that = this;

			// 显示加载提示
			uni.showLoading({
				title: '保存中...',
				mask: true
			});

			// APP版本直接下载并保存图片
			uni.downloadFile({
				url: this.imgUrl,
				success(res) {
					if (res.statusCode === 200) {
						// 下载成功，直接保存到相册
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: function () {
								uni.hideLoading();
								console.log('APP图片保存成功');
								uni.showToast({
									title: '保存成功',
									icon: 'success',
									duration: 2000
								});
								that.recordSaveAction();
							},
							fail: function (error) {
								uni.hideLoading();
								console.error('APP Save image failed:', error);

								// APP版本的权限处理
								let errorMsg = '保存失败';
								if (error.errMsg) {
									if (error.errMsg.includes('auth') || error.errMsg.includes('permission')) {
										errorMsg = '保存失败，请在系统设置中开启相册权限';
									} else if (error.errMsg.includes('system')) {
										errorMsg = '系统拒绝保存';
									}
								}

								uni.showToast({
									title: errorMsg,
									icon: 'none',
									duration: 3000
								});
							}
						});
					} else {
						uni.hideLoading();
						console.error('APP Download failed with status:', res.statusCode);
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				},
				fail(error) {
					uni.hideLoading();
					console.error('APP Download error:', error);

					let errorMsg = '图片下载失败';
					if (error.errMsg) {
						if (error.errMsg.includes('network')) {
							errorMsg = '网络连接失败';
						} else if (error.errMsg.includes('timeout')) {
							errorMsg = '下载超时';
						}
					}

					uni.showToast({
						title: errorMsg,
						icon: 'none'
					});
				}
			});
		},

		// 记录保存操作（可选的业务逻辑）
		recordSaveAction() {
			// 这里可以添加记录用户保存行为的逻辑
			// 比如上报统计数据、记录用户行为等
			console.log('用户保存了邀请图片');
		},

		// 原有的简单保存方法（保留作为备用）
		saveimg() {
			if (!this.imgUrl) {
				uni.showToast({
					title: '图片未加载',
					icon: 'none'
				});
				return;
			}
			uni.downloadFile({
				url: this.imgUrl,
				success: res => {
					if (res.statusCode === 200) {
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: () => {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								});
							},
							fail: (e) => {
								console.error('Save image failed:', e);
								uni.showToast({
									title: '图片保存失败',
									icon: 'none'
								});
							}
						});
					} else {
						console.error('Download failed:', res);
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				},
				fail: (e) => {
					console.error('Download error:', e);
					uni.showToast({
						title: '图片下载失败',
						icon: 'none'
					});
				}
			});
		},

		getImg() {
			this.$api.service.getPromoterCard().then(res => {
				console.log('QR code fetched:', res);
				this.imgUrl = res.data.qrUrl;
				this.inviteCode = res.data.qrCode;
			}).catch(e => {
				console.error('Failed to fetch QR code:', e);
				uni.showToast({
					title: '获取二维码失败',
					icon: 'none'
				});
			});
		},

		copyInviteCode() {
			if (!this.info.inviteCode) {
				uni.showToast({
					title: '无邀请码',
					icon: 'none'
				});
				return;
			}
			uni.setClipboardData({
				data: this.info.inviteCode,
				success: () => {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					});
				},
				fail: (e) => {
					console.error('Copy failed:', e);
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					});
				}
			});
		}
	},
	onLoad() {
		console.log('User info loaded:', this.info);
		this.getImg();
	}
}
</script>

<style scoped lang="scss">
.page {
	background: #f8f8f8;
	height: 100vh;
	padding: 40rpx 30rpx;

	.header {
		text-align: center;
		font-size: 52rpx;
		font-weight: 600;
		color: #000000;
	}

	.box {
		margin-top: 40rpx;
		width: 690rpx;
		height: 748rpx;
		background: #FFFFFF;
		border-radius: 32rpx;
		padding: 40rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;

		.name {
			font-size: 32rpx;
			font-weight: 600;
			color: #000000;
			text-align: center;
		}

		.desc {
			margin-top: 20rpx;
			font-size: 24rpx;
			text-align: center;
			font-weight: 400;
			color: #000000;
			padding: 0 30rpx;
		}

		image {
			width: 444rpx;
			height: 444rpx;
			margin: 18rpx auto 0;
		}

		.invite-code-container {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 20rpx;
			gap: 20rpx;
		}

		.invite-code {
			font-size: 28rpx;
			font-weight: 400;
			color: #000000;
		}

		.copy-btn {
			width: 120rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			background: #2E80FE;
			color: #FFFFFF;
			font-size: 24rpx;
			border-radius: 30rpx;
		}
	}

	.button-container {
		display: flex;
		gap: 20rpx;
		position: absolute;
		bottom: 42rpx;
		width: 690rpx;
	}

	.btn {
		flex: 1;
		height: 98rpx;
		background: #2E80FE;
		border-radius: 50rpx;
		line-height: 98rpx;
		text-align: center;
		font-size: 32rpx;
		font-weight: 500;
		color: #FFFFFF;
		cursor: pointer;
		transition: all 0.3s ease;

		&:active {
			background: #1E70EE;
			transform: scale(0.98);
		}
	}

	.save-btn {
		/* Save button uses the base .btn styles */
	}

	.share-btn {
		/* 确保分享按钮继承相同样式 */
		border: none;
		padding: 0;
		margin: 0;
		background: #2E80FE;

		/* 移除默认按钮样式 */
		&:after {
			border: none;
		}

		&[disabled] {
			background: #cccccc;
			pointer-events: none;
		}
	}

	/* APP版本单按钮样式 */
	.app-single-btn {
		justify-content: center;

		.btn {
			flex: none;
			width: 300rpx;
		}
	}
}
</style>