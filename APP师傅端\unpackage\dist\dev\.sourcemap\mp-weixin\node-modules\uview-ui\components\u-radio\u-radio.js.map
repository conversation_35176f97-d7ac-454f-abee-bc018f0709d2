{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-radio/u-radio.vue?9f1e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-radio/u-radio.vue?0f03", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-radio/u-radio.vue?f3e3", "uni-app:///node_modules/uview-ui/components/u-radio/u-radio.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-radio/u-radio.vue?13bf", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-radio/u-radio.vue?2121", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-radio/u-radio.vue?053a"], "names": ["name", "mixins", "data", "checked", "parentData", "iconSize", "labelDisabled", "disabled", "shape", "activeColor", "inactiveColor", "size", "value", "iconColor", "placement", "borderBottom", "iconPlacement", "computed", "elDisabled", "el<PERSON>abelDisabled", "elSize", "elIconSize", "elActiveColor", "elInactiveColor", "elLabelColor", "elShape", "elLabelSize", "elIconColor", "iconClasses", "classes", "iconWrapStyle", "style", "radioStyle", "uni", "mounted", "methods", "init", "updateParentData", "iconClickHandler", "wrapperClickHandler", "labelClickHandler", "emitEvent", "setRadioCheckedStatus"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqC12B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,eAqBA;EACAA;EAEAC;EACAC;IACA;MACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA,iIACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA,iHACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA,gHACA;IACA;IACAC;MACA,0GACA;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACAC;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;;MAIA;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACAA;MACAA;MACA;MACA;QACAA;MACA;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;MACA;QACA;QACAF;MACA;MACA;IACA;EACA;EACAG;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAH;MACA;MACA;MACA;IACA;IACAI;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;UACAR;QACA;MACA;IACA;IACA;IACA;IACA;IACAS;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAA6lD,CAAgB,ijDAAG,EAAC,C;;;;;;;;;;;ACAjnD;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF", "file": "node-modules/uview-ui/components/u-radio/u-radio.js", "sourcesContent": ["export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-radio.vue?vue&type=template&id=643b3322&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.radioStyle])\n  var s1 = _vm.__get_style([_vm.iconWrapStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-radio.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-radio.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t    class=\"u-radio\"\n\t\**********=\"wrapperClickHandler\"\n\t    :style=\"[radioStyle]\"\n\t    :class=\"[`u-radio-label--${parentData.iconPlacement}`, parentData.borderBottom && parentData.placement === 'column' && 'u-border-bottom']\"\n\t>\n\t\t<view\n\t\t    class=\"u-radio__icon-wrap\"\n\t\t    @tap.stop=\"iconClickHandler\"\n\t\t    :class=\"iconClasses\"\n\t\t    :style=\"[iconWrapStyle]\"\n\t\t>\n\t\t\t<slot name=\"icon\">\n\t\t\t\t<u-icon\n\t\t\t\t    class=\"u-radio__icon-wrap__icon\"\n\t\t\t\t    name=\"checkbox-mark\"\n\t\t\t\t    :size=\"elIconSize\"\n\t\t\t\t    :color=\"elIconColor\"\n\t\t\t\t/>\n\t\t\t</slot>\n\t\t</view>\n\t\t<slot>\n\t\t\t<text\n\t\t\t\tclass=\"u-radio__text\"\n\t\t\t\**********=\"labelClickHandler\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tcolor: elDisabled ? elInactiveColor : elLabelColor,\n\t\t\t\t\tfontSize: elLabelSize,\n\t\t\t\t\tlineHeight: elLabelSize\n\t\t\t\t}\"\n\t\t\t>{{label}}</text>\n\t\t</slot>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * radio 单选框\n\t * @description 单选框用于有一个选择，用户只能选择其中一个的场景。搭配u-radio-group使用\n\t * @tutorial https://www.uviewui.com/components/radio.html\n\t * @property {String | Number}\tname\t\t\tradio的名称\n\t * @property {String}\t\t\tshape\t\t\t形状，square为方形，circle为圆型\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁用\n\t * @property {String | Boolean}\tlabelDisabled\t是否禁止点击提示语选中单选框\n\t * @property {String}\t\t\tactiveColor\t\t选中时的颜色，如设置parent的active-color将失效\n\t * @property {String}\t\t\tinactiveColor\t未选中的颜色\n\t * @property {String | Number}\ticonSize\t\t图标大小，单位px\n\t * @property {String | Number}\tlabelSize\t\tlabel字体大小，单位px\n\t * @property {String | Number}\tlabel\t\t\tlabel提示文字，因为nvue下，直接slot进来的文字，由于特殊的结构，无法修改样式\n\t * @property {String | Number}\tsize\t\t\t整体的大小\n\t * @property {String}\t\t\ticonColor\t\t图标颜色\n\t * @property {String}\t\t\tlabelColor\t\tlabel的颜色\n\t * @property {Object}\t\t\tcustomStyle\t\t组件的样式，对象形式\n\t * \n\t * @event {Function} change 某个radio状态发生变化时触发(选中状态)\n\t * @example <u-radio :labelDisabled=\"false\">门掩黄昏，无计留春住</u-radio>\n\t */\n\texport default {\n\t\tname: \"u-radio\",\n\t\t\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tchecked: false,\n\t\t\t\t// 当你看到这段代码的时候，\n\t\t\t\t// 父组件的默认值，因为头条小程序不支持在computed中使用this.parent.shape的形式\n\t\t\t\t// 故只能使用如此方法\n\t\t\t\tparentData: {\n\t\t\t\t\ticonSize: 12,\n\t\t\t\t\tlabelDisabled: null,\n\t\t\t\t\tdisabled: null,\n\t\t\t\t\tshape: null,\n\t\t\t\t\tactiveColor: null,\n\t\t\t\t\tinactiveColor: null,\n\t\t\t\t\tsize: 18,\n\t\t\t\t\tvalue: null,\n\t\t\t\t\ticonColor: null,\n\t\t\t\t\tplacement: 'row',\n\t\t\t\t\tborderBottom: false,\n\t\t\t\t\ticonPlacement: 'left'\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 是否禁用，如果父组件u-raios-group禁用的话，将会忽略子组件的配置\n\t\t\telDisabled() {\n\t\t\t\treturn this.disabled !== '' ? this.disabled : this.parentData.disabled !== null ? this.parentData.disabled : false;\n\t\t\t},\n\t\t\t// 是否禁用label点击\n\t\t\telLabelDisabled() {\n\t\t\t\treturn this.labelDisabled !== '' ? this.labelDisabled : this.parentData.labelDisabled !== null ? this.parentData.labelDisabled :\n\t\t\t\t\tfalse;\n\t\t\t},\n\t\t\t// 组件尺寸，对应size的值，默认值为21px\n\t\t\telSize() {\n\t\t\t\treturn this.size ? this.size : (this.parentData.size ? this.parentData.size : 21);\n\t\t\t},\n\t\t\t// 组件的勾选图标的尺寸，默认12px\n\t\t\telIconSize() {\n\t\t\t\treturn this.iconSize ? this.iconSize : (this.parentData.iconSize ? this.parentData.iconSize : 12);\n\t\t\t},\n\t\t\t// 组件选中激活时的颜色\n\t\t\telActiveColor() {\n\t\t\t\treturn this.activeColor ? this.activeColor : (this.parentData.activeColor ? this.parentData.activeColor : '#2979ff');\n\t\t\t},\n\t\t\t// 组件选未中激活时的颜色\n\t\t\telInactiveColor() {\n\t\t\t\treturn this.inactiveColor ? this.inactiveColor : (this.parentData.inactiveColor ? this.parentData.inactiveColor :\n\t\t\t\t\t'#c8c9cc');\n\t\t\t},\n\t\t\t// label的颜色\n\t\t\telLabelColor() {\n\t\t\t\treturn this.labelColor ? this.labelColor : (this.parentData.labelColor ? this.parentData.labelColor : '#606266')\n\t\t\t},\n\t\t\t// 组件的形状\n\t\t\telShape() {\n\t\t\t\treturn this.shape ? this.shape : (this.parentData.shape ? this.parentData.shape : 'circle');\n\t\t\t},\n\t\t\t// label大小\n\t\t\telLabelSize() {\n\t\t\t\treturn uni.$u.addUnit(this.labelSize ? this.labelSize : (this.parentData.labelSize ? this.parentData.labelSize :\n\t\t\t\t\t'15'))\n\t\t\t},\n\t\t\telIconColor() {\n\t\t\t\tconst iconColor = this.iconColor ? this.iconColor : (this.parentData.iconColor ? this.parentData.iconColor :\n\t\t\t\t\t'#ffffff');\n\t\t\t\t// 图标的颜色\n\t\t\t\tif (this.elDisabled) {\n\t\t\t\t\t// disabled状态下，已勾选的radio图标改为elInactiveColor\n\t\t\t\t\treturn this.checked ? this.elInactiveColor : 'transparent'\n\t\t\t\t} else {\n\t\t\t\t\treturn this.checked ? iconColor : 'transparent'\n\t\t\t\t}\n\t\t\t},\n\t\t\ticonClasses() {\n\t\t\t\tlet classes = []\n\t\t\t\t// 组件的形状\n\t\t\t\tclasses.push('u-radio__icon-wrap--' + this.elShape)\n\t\t\t\tif (this.elDisabled) {\n\t\t\t\t\tclasses.push('u-radio__icon-wrap--disabled')\n\t\t\t\t}\n\t\t\t\tif (this.checked && this.elDisabled) {\n\t\t\t\t\tclasses.push('u-radio__icon-wrap--disabled--checked')\n\t\t\t\t}\n\t\t\t\t// 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\n\t\t\t\t// #ifdef MP-ALIPAY || MP-TOUTIAO\n\t\t\t\tclasses = classes.join(' ')\n\t\t\t\t// #endif\n\t\t\t\treturn classes\n\t\t\t},\n\t\t\ticonWrapStyle() {\n\t\t\t\t// radio的整体样式\n\t\t\t\tconst style = {}\n\t\t\t\tstyle.backgroundColor = this.checked && !this.elDisabled ? this.elActiveColor : '#ffffff'\n\t\t\t\tstyle.borderColor = this.checked && !this.elDisabled ? this.elActiveColor : this.elInactiveColor\n\t\t\t\tstyle.width = uni.$u.addUnit(this.elSize)\n\t\t\t\tstyle.height = uni.$u.addUnit(this.elSize)\n\t\t\t\t// 如果是图标在右边的话，移除它的右边距\n\t\t\t\tif (this.parentData.iconPlacement === 'right') {\n\t\t\t\t\tstyle.marginRight = 0\n\t\t\t\t}\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tradioStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\tif(this.parentData.borderBottom && this.parentData.placement === 'row') {\n\t\t\t\t\tuni.$u.error('检测到您将borderBottom设置为true，需要同时将u-radio-group的placement设置为column才有效')\n\t\t\t\t}\n\t\t\t\t// 当父组件设置了显示下边框并且排列形式为纵向时，给内容和边框之间加上一定间隔\n\t\t\t\tif(this.parentData.borderBottom && this.parentData.placement === 'column') {\n\t\t\t\t\t// ios像素密度高，需要多一点的距离\n\t\t\t\t\tstyle.paddingBottom = uni.$u.os() === 'ios' ? '12px' : '8px'\n\t\t\t\t}\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 支付宝小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环引用\n\t\t\t\tthis.updateParentData()\n\t\t\t\tif (!this.parent) {\n\t\t\t\t\tuni.$u.error('u-radio必须搭配u-radio-group组件使用')\n\t\t\t\t}\n\t\t\t\t// 设置初始化时，是否默认选中的状态\n\t\t\t\tthis.checked = this.name === this.parentData.value\n\t\t\t},\n\t\t\tupdateParentData() {\n\t\t\t\tthis.getParentData('u-radio-group')\n\t\t\t},\n\t\t\t// 点击图标\n\t\t\ticonClickHandler(e) {\n\t\t\t\tthis.preventEvent(e)\n\t\t\t\t// 如果整体被禁用，不允许被点击\n\t\t\t\tif (!this.elDisabled) {\n\t\t\t\t\tthis.setRadioCheckedStatus()\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 横向两端排列时，点击组件即可触发选中事件\n\t\t\twrapperClickHandler(e) {\n\t\t\t\tthis.parentData.iconPlacement === 'right' && this.iconClickHandler(e)\n\t\t\t},\n\t\t\t// 点击label\n\t\t\tlabelClickHandler(e) {\n\t\t\t\tthis.preventEvent(e)\n\t\t\t\t// 如果按钮整体被禁用或者label被禁用，则不允许点击文字修改状态\n\t\t\t\tif (!this.elLabelDisabled && !this.elDisabled) {\n\t\t\t\t\tthis.setRadioCheckedStatus()\n\t\t\t\t}\n\t\t\t},\n\t\t\temitEvent() {\n\t\t\t\t// u-radio的checked不为true时(意味着未选中)，才发出事件，避免多次点击触发事件\n\t\t\t\tif (!this.checked) {\n\t\t\t\t\tthis.$emit('change', this.name)\n\t\t\t\t\t// 尝试调用u-form的验证方法，进行一定延迟，否则微信小程序更新可能会不及时\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tuni.$u.formValidate(this, 'change')\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 改变组件选中状态\n\t\t\t// 这里的改变的依据是，更改本组件的checked值为true，同时通过父组件遍历所有u-radio实例\n\t\t\t// 将本组件外的其他u-radio的checked都设置为false(都被取消选中状态)，因而只剩下一个为选中状态\n\t\t\tsetRadioCheckedStatus() {\n\t\t\t\tthis.emitEvent()\n\t\t\t\t// 将本组件标记为选中状态\n\t\t\t\tthis.checked = true\n\t\t\t\ttypeof this.parent.unCheckedOther === 'function' && this.parent.unCheckedOther(this)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\t$u-radio-wrap-margin-right:6px !default;\n\t$u-radio-wrap-font-size:20px !default;\n\t$u-radio-wrap-border-width:1px !default;\n\t$u-radio-wrap-border-color: #c8c9cc !default;\n\t$u-radio-line-height:0 !default;\n\t$u-radio-circle-border-radius:100% !default;\n\t$u-radio-square-border-radius:3px !default;\n\t$u-radio-checked-color:#fff !default;\n\t$u-radio-checked-background-color:red !default;\n\t$u-radio-checked-border-color: #2979ff !default;\n\t$u-radio-disabled-background-color:#ebedf0 !default;\n\t$u-radio-disabled--checked-color:#c8c9cc !default;\n\t$u-radio-label-margin-left: 5px !default;\n\t$u-radio-label-margin-right:12px !default;\n\t$u-radio-label-color:$u-content-color !default;\n\t$u-radio-label-font-size:15px !default;\n\t$u-radio-label-disabled-color:#c8c9cc !default;\n\t\n\t.u-radio {\n\t\t/* #ifndef APP-NVUE */\n\t\t@include flex(row);\n\t\t/* #endif */\n\t\toverflow: hidden;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\n\t\t&-label--left {\n\t\t\tflex-direction: row\n\t\t}\n\n\t\t&-label--right {\n\t\t\tflex-direction: row-reverse;\n\t\t\tjustify-content: space-between\n\t\t}\n\n\t\t&__icon-wrap {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tbox-sizing: border-box;\n\t\t\t// nvue下，border-color过渡有问题\n\t\t\ttransition-property: border-color, background-color, color;\n\t\t\ttransition-duration: 0.2s;\n\t\t\t/* #endif */\n\t\t\tcolor: $u-content-color;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tcolor: transparent;\n\t\t\ttext-align: center;\n\t\t\tmargin-right: $u-radio-wrap-margin-right;\n\t\t\tfont-size: $u-radio-wrap-font-size;\n\t\t\tborder-width: $u-radio-wrap-border-width;\n\t\t\tborder-color: $u-radio-wrap-border-color;\n\t\t\tborder-style: solid;\n\n\t\t\t/* #ifdef MP-TOUTIAO */\n\t\t\t// 头条小程序兼容性问题，需要设置行高为0，否则图标偏下\n\t\t\t&__icon {\n\t\t\t\tline-height: $u-radio-line-height;\n\t\t\t}\n\n\t\t\t/* #endif */\n\n\t\t\t&--circle {\n\t\t\t\tborder-radius: $u-radio-circle-border-radius;\n\t\t\t}\n\n\t\t\t&--square {\n\t\t\t\tborder-radius: $u-radio-square-border-radius;\n\t\t\t}\n\n\t\t\t&--checked {\n\t\t\t\tcolor: $u-radio-checked-color;\n\t\t\t\tbackground-color: $u-radio-checked-background-color;\n\t\t\t\tborder-color: $u-radio-checked-border-color;\n\t\t\t}\n\n\t\t\t&--disabled {\n\t\t\t\tbackground-color: $u-radio-disabled-background-color !important;\n\t\t\t}\n\n\t\t\t&--disabled--checked {\n\t\t\t\tcolor: $u-radio-disabled--checked-color !important;\n\t\t\t}\n\t\t}\n\n\t\t&__label {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tword-wrap: break-word;\n\t\t\t/* #endif */\n\t\t\tmargin-left: $u-radio-label-margin-left;\n\t\t\tmargin-right: $u-radio-label-margin-right;\n\t\t\tcolor: $u-radio-label-color;\n\t\t\tfont-size: $u-radio-label-font-size;\n\n\t\t\t&--disabled {\n\t\t\t\tcolor: $u-radio-label-disabled-color;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-radio.vue?vue&type=style&index=0&id=643b3322&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-radio.vue?vue&type=style&index=0&id=643b3322&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755133979102\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./u-radio.vue?vue&type=template&id=643b3322&scoped=true&\"\nvar renderjs\nimport script from \"./u-radio.vue?vue&type=script&lang=js&\"\nexport * from \"./u-radio.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-radio.vue?vue&type=style&index=0&id=643b3322&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"643b3322\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-radio/u-radio.vue\"\nexport default component.exports"], "sourceRoot": ""}