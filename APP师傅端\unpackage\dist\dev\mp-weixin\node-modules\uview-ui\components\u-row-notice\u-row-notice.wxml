<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-notice data-v-4661e472" bindtap="__e"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><block wx:if="{{icon}}"><view class="u-notice__left-icon data-v-4661e472"><u-icon vue-id="79912e6d-1" name="{{icon}}" color="{{color}}" size="19" class="data-v-4661e472" bind:__l="__l"></u-icon></view></block></block><view data-ref="u-notice__content" class="u-notice__content data-v-4661e472 vue-ref"><view data-ref="u-notice__content__text" class="u-notice__content__text data-v-4661e472 vue-ref" style="{{$root.s0}}"><block wx:for="{{innerText}}" wx:for-item="item" wx:for-index="index" wx:key="index"><text style="{{$root.s1}}" class="data-v-4661e472">{{item}}</text></block></view></view><block wx:if="{{$root.g0}}"><view class="u-notice__right-icon data-v-4661e472"><block wx:if="{{mode==='link'}}"><u-icon vue-id="79912e6d-2" name="arrow-right" size="{{17}}" color="{{color}}" class="data-v-4661e472" bind:__l="__l"></u-icon></block><block wx:if="{{mode==='closable'}}"><u-icon vue-id="79912e6d-3" name="close" size="{{16}}" color="{{color}}" data-event-opts="{{[['^click',[['close']]]]}}" bind:click="__e" class="data-v-4661e472" bind:__l="__l"></u-icon></block></view></block></view>