{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_my.vue?405c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_my.vue?4fe8", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_my.vue?2e11", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_my.vue?c23f", "uni-app:///shifu/master_order_my.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_my.vue?75a3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/master_order_my.vue?0bb5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "ready", "id", "selectedItem", "payType", "Info", "settingOrders", "settingOrderList", "aftermarket", "remark", "imageErrors", "fallbackImage", "showImageModal", "currentImage", "computed", "orderDetails", "label", "value", "alignRight", "methods", "formatMobile", "formatAddress", "formatHouseNumber", "formatOrderCode", "isValidImageUrl", "splitImageUrls", "godh", "uni", "latitude", "longitude", "scale", "name", "address", "success", "fail", "phoneDLD", "title", "icon", "phoneNumber", "getDetail", "res", "console", "text", "orderCode", "goodsName", "createTime", "mobile", "addressInfo", "houseNumber", "lat", "lng", "onImageError", "urls", "previewImage", "currentItem", "current", "closeImageModal", "onLoad", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgFl3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;UAAAC;QAAA;MACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA,QACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QAAA;MAAA;MACA;MACA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;MACA;QAAA;MAAA;QAAA;MAAA;IACA;IACA;IACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UAAA;QAAA;QACAC;UAAA;QAAA;MACA;IACA;IACA;IACAC;MACA;QACAR;UACAS;UACAC;QACA;QACA;MACA;MACAV;QACAW;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAC;gBACAA;gBACAA;gBACAA;;gBAEA;kBACAd;oBACAS;oBACAC;kBACA;gBACA;gBACA;kBACAV;oBACAS;oBACAC;kBACA;gBACA;;gBAEA;gBACA;kBACA/B;kBACAC;kBAAA;kBACAC;oBAAAC;kBAAA;kBACAiC;gBAAA,GACAF,SACA;gBAEA;kBACAG;kBACAC;kBACAC;kBACAC;kBACAC;kBACAf;kBACAgB;kBACAC;kBACAC;gBACA;;gBAEA;gBACAT;gBACAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAd;kBACAS;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAc;MACAV;MACA;;MAEA;MACA;QACA;QACA;UACA;UACAW;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;;MAEA;MAAA,2CACA;QAAA;MAAA;QAAA;UAAA;UACA;YACAC;cAAA;YAAA;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;QACA3B;UACAyB;UACAG;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cACAhB;cACAA;cACAA;cAAA,IAEA;gBAAA;gBAAA;cAAA;cACAA;cACAd;gBACAS;gBACAC;cACA;cAAA;YAAA;cAAA;cAAA,OAIA;YAAA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAqB;IACA/B;IACAc;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvSA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/master_order_my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/master_order_my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./master_order_my.vue?vue&type=template&id=ba51cf60&scoped=true&\"\nvar renderjs\nimport script from \"./master_order_my.vue?vue&type=script&lang=js&\"\nexport * from \"./master_order_my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./master_order_my.vue?vue&type=style&index=0&id=ba51cf60&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ba51cf60\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/master_order_my.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_my.vue?vue&type=template&id=ba51cf60&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.ready\n    ? _vm.Info.settingOrders && _vm.Info.settingOrders.length > 0\n    : null\n  var l2 =\n    _vm.ready && g0\n      ? _vm.__map(_vm.Info.settingOrders, function (order, orderIndex) {\n          var $orig = _vm.__get_orig(order)\n          var g1 = order.settingOrderList && order.settingOrderList.length > 0\n          var l1 = _vm.__map(order.settingOrderList, function (item, index) {\n            var $orig = _vm.__get_orig(item)\n            var m0 = g1 ? item.val && _vm.isValidImageUrl(item.val) : null\n            var l0 =\n              g1 && m0\n                ? _vm.__map(\n                    _vm.splitImageUrls(item.val),\n                    function (url, imgIndex) {\n                      var $orig = _vm.__get_orig(url)\n                      var g2 = url.trim()\n                      return {\n                        $orig: $orig,\n                        g2: g2,\n                      }\n                    }\n                  )\n                : null\n            var g3 =\n              g1 && !m0 ? item.val && item.val.toString().trim() !== \"\" : null\n            return {\n              $orig: $orig,\n              m0: m0,\n              l0: l0,\n              g3: g3,\n            }\n          })\n          return {\n            $orig: $orig,\n            g1: g1,\n            l1: l1,\n          }\n        })\n      : null\n  var g4 = _vm.ready\n    ? !_vm.Info.settingOrders || _vm.Info.settingOrders.length === 0\n    : null\n  var g5 = _vm.ready ? _vm.Info.text && _vm.Info.text.trim() !== \"\" : null\n  var g6 = _vm.ready\n    ? _vm.Info.aftermarket &&\n      _vm.Info.aftermarket.remark &&\n      _vm.Info.aftermarket.remark.trim() !== \"\"\n    : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, info) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        info = _temp2.info\n      var _temp, _temp2\n      info.label === \"联系方式\" ? _vm.phoneDLD(info.value) : null\n    }\n    _vm.e1 = function ($event, url, index, imgIndex) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        url = _temp4.url,\n        index = _temp4.index,\n        imgIndex = _temp4.imgIndex\n      var _temp3, _temp4\n      return _vm.onImageError(url, index, imgIndex)\n    }\n    _vm.e2 = function ($event, url) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        url = _temp6.url\n      var _temp5, _temp6\n      return _vm.previewImage(url)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l2: l2,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_my.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\" v-if=\"ready\">\n    <view class=\"box\">\n      <!-- Title Section -->\n      <view class=\"title\">\n        <image src=\"../static/images/8957.png\" mode=\"aspectFill\" class=\"title-icon\"></image>\n        <span>订单信息</span>\n      </view>\n\n      <!-- Order Info Section -->\n      <view class=\"info-box\">\n        <view class=\"info-item\" v-for=\"(info, index) in orderDetails\" :key=\"index\">\n          <text class=\"label\">{{ info.label }}</text>\n          <text :class=\"['value', info.alignRight ? 'align-right' : '']\"\n            @click=\"info.label === '联系方式' ? phoneDLD(info.value) : null\">{{ info.value }}</text>\n        </view>\n        <view class=\"navigation-btn\" @click=\"godh()\">\n          <image src=\"../static/images/9349.png\" mode=\"aspectFill\" class=\"nav-icon\"></image>\n          <text>导航</text>\n        </view>\n      </view>\n\n      <!-- Dynamic Info Section (Images/Text) -->\n      <view class=\"dynamic-section\" v-if=\"Info.settingOrders && Info.settingOrders.length > 0\">\n        <view v-for=\"(order, orderIndex) in Info.settingOrders\" :key=\"orderIndex\">\n          <view class=\"order-info\" v-if=\"order.num\">\n            <view class=\"title\">订单数量: {{ order.num }}</view>\n          </view>\n          <view v-if=\"order.settingOrderList && order.settingOrderList.length > 0\"\n            v-for=\"(item, index) in order.settingOrderList\" :key=\"index\" class=\"setting-item\">\n            <view class=\"title\">{{ item.problemDesc || '详情信息' }}</view>\n            <view class=\"img-box\" v-if=\"item.val && isValidImageUrl(item.val)\">\n              <image v-for=\"(url, imgIndex) in splitImageUrls(item.val)\" :key=\"imgIndex\" :src=\"url.trim()\"\n                mode=\"aspectFill\" @error=\"onImageError(url, index, imgIndex)\" @click=\"previewImage(url)\"\n                class=\"dynamic-image\"></image>\n            </view>\n            <view v-else-if=\"item.val && item.val.toString().trim() !== ''\" class=\"text-box\">\n              <text>{{ item.val }}</text>\n            </view>\n            <view v-else class=\"text-box\">\n              <text>无</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 如果没有动态内容，显示提示 -->\n      <view v-if=\"!Info.settingOrders || Info.settingOrders.length === 0\" class=\"dynamic-section\">\n        <view class=\"title\">详情信息</view>\n        <view class=\"text-box\">\n          <text>暂无详情信息</text>\n        </view>\n      </view>\n\n      <!-- Notes Section -->\n      <view class=\"title\">备注</view>\n      <view class=\"notes-box\">\n        <text>{{ Info.text && Info.text.trim() !== '' ? Info.text : '无' }}</text>\n      </view>\n\n      <!-- 售后信息 -->\n      <view v-if=\"Info.aftermarket && Info.aftermarket.remark && Info.aftermarket.remark.trim() !== ''\" class=\"\">\n        <view class=\"title\">售后</view>\n        <view class=\"notes-box\">\n          <text>{{ Info.aftermarket.remark ? Info.aftermarket.remark : '无' }}</text>\n        </view>\n      </view>\n\n      <!-- Image Preview Modal -->\n      <view class=\"image-modal\" v-if=\"showImageModal\" @click=\"closeImageModal\">\n        <view class=\"modal-content\" @click.stop>\n          <image :src=\"currentImage\" mode=\"aspectFit\" class=\"modal-image\"></image>\n          <view class=\"close-btn\" @click=\"closeImageModal\">关闭</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      ready: false,\n      id: '',\n      selectedItem: {},\n      payType: {},\n      Info: {\n        settingOrders: [], // 初始化为空数组\n        settingOrderList: [], // 保留兼容性\n        aftermarket: { remark: '' } // 初始化售后信息\n      },\n      imageErrors: [], // Track images that failed to load\n      fallbackImage: '../static/images/placeholder.png', // Placeholder image path\n      showImageModal: false, // Control image modal visibility\n      currentImage: '' // Store the currently displayed image URL\n    };\n  },\n  computed: {\n    orderDetails() {\n      return [\n        { label: '订单单号', value: this.formatOrderCode(this.selectedItem.orderCode), alignRight: true },\n        { label: '服务内容', value: this.selectedItem.goodsName || '' },\n        { label: '下单时间', value: this.selectedItem.createTime || '' },\n        { label: '联系方式', value: this.formatMobile(this.selectedItem.mobile) },\n        { label: '服务定位', value: this.formatAddress(this.selectedItem.addressInfo), alignRight: true },\n        { label: '服务地址', value: this.formatAddress(this.selectedItem.address), alignRight: true },\n        { label: '门牌号', value: this.formatHouseNumber(this.selectedItem.houseNumber), alignRight: true }\n      ];\n    }\n  },\n  methods: {\n    // Format mobile number with ***** in the middle, unless payType.payType is 7, 3, 5, or 6\n    formatMobile(mobile) {\n      if (!mobile) return '';\n      if (this.payType.payType) return mobile;\n\n      return mobile;\n    },\n    // Format address to hide last 7 characters, unless payType.payType is 7, 3, 5, or 6\n    formatAddress(address) {\n      if (!address) return '';\n      if (this.payType.payType) return address;\n\n      return address;\n    },\n    // Format house number with ***** in the middle, unless payType.payType is 7, 3, 5, or 6\n    formatHouseNumber(houseNumber) {\n      if (!houseNumber) return '';\n      if (this.payType.payType) return houseNumber;\n\n      return houseNumber;\n    },\n    // Format orderCode (keeping original format but removing '无')\n    formatOrderCode(orderCode) {\n      return orderCode || '';\n    },\n    // Validate if the URL(s) contain valid image URLs\n    isValidImageUrl(val) {\n      if (!val || (typeof val !== 'string' && typeof val !== 'number')) return false;\n      const valStr = val.toString();\n      const urls = valStr.split(',').map(url => url.trim());\n      const imageRegex = /^(https?:\\/\\/).*\\.(png|jpg|jpeg|gif|bmp|webp)(\\?.*)?$/i;\n      return urls.some(url => imageRegex.test(url));\n    },\n    // Split comma-separated image URLs\n    splitImageUrls(val) {\n      if (!val) return [];\n      const valStr = val.toString();\n      return valStr.split(',').map(url => url.trim()).filter(url => url !== '');\n    },\n    // Handle navigation\n    godh() {\n      uni.openLocation({\n        latitude: Number(this.selectedItem.lat) || 0,\n        longitude: Number(this.selectedItem.lng) || 0,\n        scale: 18,\n        name: this.selectedItem.address || '未知地址',\n        address: this.selectedItem.addressInfo || '未知地址信息',\n        success: () => console.log('Navigation opened'),\n        fail: err => console.error('Navigation error:', err)\n      });\n    },\n    // Make phone call\n    phoneDLD(phoneNumber) {\n      if (!phoneNumber || phoneNumber.includes('*')) {\n        uni.showToast({\n          title: '无法拨打电话，号码不可用',\n          icon: 'none'\n        });\n        return;\n      }\n      uni.makePhoneCall({\n        phoneNumber: phoneNumber,\n      });\n    },\n    // Fetch order details\n    async getDetail() {\n      try {\n        const res = await this.$api.shifu.orderdetM(this.id);\n        console.log('API Response:', res);\n        console.log('coachStatus:', res.data.coachStatus);\n        console.log('settingOrders:', res.data.settingOrders); // 新的数据结构\n        console.log('settingOrderList:', res.data.settingOrderList); // 旧的数据结构（兼容性）\n\n        if (res.data.coachStatus === 1) {\n          uni.showToast({\n            title: '师傅状态在审核中',\n            icon: 'none'\n          });\n        }\n        if (res.data.coachStatus === -1 || res.data.coachStatus === 4) {\n          uni.showToast({\n            title: '你还不是师傅',\n            icon: 'none'\n          });\n        }\n\n        // 确保数据结构完整\n        this.Info = {\n          settingOrders: res.data.settingOrders || [],\n          settingOrderList: res.data.settingOrderList || [], // 保留兼容性\n          aftermarket: res.data.aftermarket || { remark: '' },\n          text: res.data.text || '',\n          ...res.data\n        };\n\n        this.selectedItem = {\n          orderCode: res.data.orderCode || '',\n          goodsName: res.data.goodsName || '',\n          createTime: res.data.createTime || 0,\n          mobile: res.data.mobile || '',\n          addressInfo: res.data.addressInfo || '',\n          address: res.data.address || '',\n          houseNumber: res.data.houseNumber || '',\n          lat: res.data.lat || 0,\n          lng: res.data.lng || 0\n        };\n\n        // 调试信息\n        console.log('Final Info object:', this.Info);\n        console.log('settingOrderList length:', this.Info.settingOrderList?.length);\n\n      } catch (err) {\n        console.error('API Error:', err);\n        uni.showToast({\n          title: '获取订单详情失败',\n          icon: 'none'\n        });\n      }\n    },\n    // Handle image load errors\n    onImageError(url, index, imgIndex) {\n      console.error(`Failed to load image: ${url}`);\n      this.imageErrors.push(`${index}-${imgIndex}`);\n\n      // Find the item in settingOrders structure\n      for (let orderIndex = 0; orderIndex < this.Info.settingOrders.length; orderIndex++) {\n        const order = this.Info.settingOrders[orderIndex];\n        if (order.settingOrderList && order.settingOrderList[index]) {\n          const urls = this.splitImageUrls(order.settingOrderList[index].val);\n          urls[imgIndex] = this.fallbackImage;\n          this.$set(order.settingOrderList[index], 'val', urls.join(','));\n          break;\n        }\n      }\n    },\n    // Show image using uni.previewImage\n    previewImage(url) {\n      let currentItem = null;\n\n      // Search in settingOrders structure\n      for (const order of this.Info.settingOrders) {\n        if (order.settingOrderList) {\n          currentItem = order.settingOrderList.find(item => item.val && item.val.toString().includes(url));\n          if (currentItem) break;\n        }\n      }\n\n      if (currentItem) {\n        uni.previewImage({\n          urls: this.splitImageUrls(currentItem.val),\n          current: url,\n        });\n      }\n    },\n    // Close image modal\n    closeImageModal() {\n      this.showImageModal = false;\n      this.currentImage = '';\n    }\n  },\n  async onLoad(options) {\n    this.id = options.id || '';\n    this.payType = uni.getStorageSync('orderdetails') || {};\n    console.log('payType:', this.payType);\n    console.log('payType.payType:', this.payType.payType);\n    console.log('Page options:', options);\n\n    if (!this.id) {\n      console.error('No order ID provided');\n      uni.showToast({\n        title: '订单ID缺失',\n        icon: 'none'\n      });\n      return;\n    }\n\n    await this.getDetail();\n    this.ready = true;\n  },\n  onUnload() {\n    uni.removeStorageSync('orderdetails');\n    console.log('Removed orderdetails from local storage');\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f5f7fa 0%, #e4e7ed 100%);\n  padding: 32rpx 24rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n}\n\n.box {\n  margin: 0 auto;\n  max-width: 690rpx;\n  background: #ffffff;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  padding: 32rpx;\n}\n\n/* Title Styling */\n.title {\n  display: flex;\n  align-items: center;\n  font-size: 36rpx;\n  font-weight: 700;\n  color: #1a1a1a;\n  margin-bottom: 24rpx;\n\n  .title-icon {\n    width: 48rpx;\n    height: 48rpx;\n    margin-right: 16rpx;\n  }\n}\n\n/* Info Box Styling */\n.info-box {\n  background: #fafafa;\n  border-radius: 12rpx;\n  padding: 24rpx;\n  margin-bottom: 32rpx;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16rpx 0;\n  border-bottom: 1rpx solid #e5e7eb;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  .label {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #6b7280;\n  }\n\n  .value {\n    font-size: 30rpx;\n    font-weight: 600;\n    color: #1a1a1a;\n    max-width: 420rpx;\n    white-space: normal;\n    word-break: break-all;\n    line-height: 1.4;\n\n    &.align-right {\n      text-align: right;\n    }\n  }\n}\n\n.navigation-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 180rpx;\n  height: 64rpx;\n  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);\n  border-radius: 32rpx;\n  color: #ffffff;\n  font-size: 28rpx;\n  font-weight: 500;\n  margin-top: 24rpx;\n  margin-left: auto;\n\n  .nav-icon {\n    width: 32rpx;\n    height: 32rpx;\n    margin-right: 12rpx;\n  }\n}\n\n/* Dynamic Section Styling */\n.dynamic-section {\n  margin-bottom: 32rpx;\n}\n\n.order-info {\n  margin-bottom: 16rpx;\n  padding: 16rpx;\n  background: #f0f9ff;\n  border-radius: 8rpx;\n \n}\n\n.setting-item {\n  margin-bottom: 24rpx;\n  padding-bottom: 16rpx;\n  border-bottom: 1rpx solid #f3f4f6;\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.img-box {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16rpx;\n  margin-top: 24rpx;\n}\n\n.dynamic-image {\n  width: 196rpx;\n  height: 196rpx;\n  border-radius: 16rpx;\n  border: 1rpx solid #e5e7eb;\n  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\n}\n\n/* Image Modal Styling */\n.image-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 100vw;\n  height: 100vh;\n  position: relative;\n}\n\n.modal-image {\n  width: 100vw;\n  height: 100vh;\n  object-fit: contain;\n  /* Ensures image scales to fit without distortion */\n  border-radius: 0;\n  /* Remove border-radius for full-screen effect */\n}\n\n.close-btn {\n  position: absolute;\n  bottom: 20rpx;\n  padding: 16rpx 32rpx;\n  background: #ffffff;\n  border-radius: 32rpx;\n  font-size: 28rpx;\n  color: #1a1a1a;\n  font-weight: 500;\n  z-index: 1100;\n  /* Ensure button is above image */\n}\n\n/* Text and Notes Styling */\n.text-box {\n  margin-top: 24rpx;\n  background: #f3f4f6;\n  border-radius: 12rpx;\n  padding: 16rpx 24rpx;\n  font-size: 28rpx;\n  color: #4b5563;\n}\n\n.notes-box {\n  margin-top: 24rpx;\n  background: #f9fafb;\n  border-radius: 16rpx;\n  padding: 24rpx 32rpx;\n  font-size: 28rpx;\n  color: #374151;\n  line-height: 1.5;\n  word-break: break-all;\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_my.vue?vue&type=style&index=0&id=ba51cf60&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./master_order_my.vue?vue&type=style&index=0&id=ba51cf60&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755137408912\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}