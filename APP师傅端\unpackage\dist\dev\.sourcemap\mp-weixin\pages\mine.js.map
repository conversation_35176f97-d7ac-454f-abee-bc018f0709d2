{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?e8ec", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?0f9b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?04ef", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?f54e", "uni-app:///pages/mine.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?d454", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/mine.vue?1612"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "args", "clearTimeout", "timeout", "components", "tabbar", "data", "isLoading", "inviteCode", "tmplIds", "code", "labelName", "loginPopupVisible", "agreedToTerms", "shifusta<PERSON>", "bindPhonePopupVisible", "isBindingPhone", "bindPhoneSmsCountdown", "bindPhoneSmsTimer", "bindPhoneForm", "phone", "orderList", "icon", "text", "url", "count", "orderList3", "toolList2", "iconColor", "computed", "userInfo", "token", "er<PERSON><PERSON>", "regeocode", "isLoggedIn", "console", "hasToken", "hasUserInfo", "result", "displayUserInfo", "avatarUrl", "nick<PERSON><PERSON>", "userId", "shifuId", "pid", "statusText", "status", "statusBadgeClass", "canBindPhone", "watch", "handler", "deep", "immediate", "onLoad", "uni", "provider", "success", "fail", "onShow", "setTimeout", "onPullDownRefresh", "Promise", "methods", "loadCachedLabelName", "getmyG<PERSON>", "getmylogin", "getNowPosition", "type", "isHighAccuracy", "accuracy", "requestError", "amapResponse", "province", "city", "adcode", "position", "lat", "lng", "city_id", "getshifuinfo", "mobile", "address", "cityId", "labelId", "<PERSON><PERSON><PERSON>", "messagePush", "key", "val", "debounceGetHighlight", "getHighlight", "role", "item", "index", "handleContact", "showLoginPopup", "hideLoginPopup", "toggleAgreement", "navigateToAgreement", "initUserData", "loadUserRelatedData", "fetchShifuInfo", "fetchShifuInfoImmediately", "handleNavigate", "navigateTo", "showToast", "title", "duration", "saveUserInfoToStorage", "fetchUserInfo", "then", "createTime", "catch", "finally", "handleInvalidSession", "showBindPhonePopup", "hideBindPhonePopup", "clearInterval", "validatePhone", "sendBindPhoneSmsCode", "response", "startBindPhoneCountdown", "handleBindPhone", "unionid", "registerID", "params", "shortCode", "platform", "registrationId", "updatedUserInfo", "shiInfo", "onGetPhoneNumber", "mask", "e", "encryptedData", "iv", "loginWithWeixin", "userInfoRes", "initialUserInfo", "shifuStatusRes", "userinster", "registerRes", "newStatusRes", "masterRess", "masterRes", "shiInfoData", "modalShownKey", "hasShownModal", "cachedShiInfo", "shiInfoResponses", "shiInfoResponse", "defaultUserInfo", "content", "confirmText", "cancelText", "cancelable"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAm1B,CAAgB,m2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;ACiNv2B;AAGA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA,CAAC;AAAD;AAEA;AACA;EACA;EACA;IAAA;MAAAC;IAAA;IACA;IACAC;IACAC;MAAA;IAAA;EACA;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC,UACA,gDACA,+CACA,8CACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAV;MACA;MACAW;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC;QACAJ;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MACAG;QACAL;QACAC;QACAC;QACAI;MACA,GACA;QACAN;QACAC;QACAC;QACAI;MACA;IAEA;EACA;EACAC,0CACA;IACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;MACA;MACA;;MAEA;MACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;MAEA;IACA;IACA;IACA;IACAC;MACA;;MAEA;MACA,oFACA;MAEA,mFACA;MAEA,uEACA;MAEA;MACA;MACA;MAEA;QACAnB;QACAoB;QACAC;QACAC;QACAC;QACAC;MACA;MAEAT;MACA;IACA;IACAU;MAAA;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;UACA;UACA;YACAC;YACA;YACA;cACA;cACAX;YACA;UACA;QACA;UACAA;QACA;MACA;;MAEA;MACAW;;MAEA;MACA;QACAX;QACAW;;QAEA;QACA;UACAX;UACA;UACA;YACA;cACA;YACA;UACA;QACA;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACAA;UACA;QAAA;MAAA;IAEA;IACAY;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA,8DACA;IACA;EAAA,EACA;EACAC;IACA;IACAnB;MACAoB;QAAA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;QACA;MACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;MACAlB;MACA;MACAmB;IACA;IACA;MACAnB;MACA;MACAmB;IACA;MACA;MACA;QACAnB;QACA;QACA;QACAmB;MACA;IACA;;IAEA;IACA;IAEAA;MACAC;MACAC;QACA;UACA;UACArB;QACA;MACA;MACAsB;QACAtB;MACA;IAEA;;IAEA;IACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;IACA;EACA;EACAuB;IAAA;IACAvB;IACAA;IACAA;IACAA;IACAA;IACAA;;IAEA;IACA;;IAEA;IACA;;IAEA;IACA;MACAA;MACA;MACA;IACA;;IAEA;IACA;MACAA;MACA;MACA;MACAwB;QACA;UACA;QACA;MACA;IACA;MACA;MACA;IACA;IAEA;MACA;IACA;EACA;EACAC;IACA;IACA;MACAC,aACA,sBACA,qBACA,uBACA;MAAA,CACA;QACAP;QACA;MACA;QACAnB;QACAmB;MACA;IACA;MACA;MACA;MACAA;MACA;IACA;EACA;EACAQ;IACA;IACAC;MACA;MACA;QACA;QACA5B;MACA;IACA;IAEA6B;MAAA;MACA;MACA;MAEA;QACA7B;QACA;UACA;UACA;UACAmB;UACAnB;QACA;MACA;QACAA;QACA;QACA;UACA;QACA;MACA;IACA;IACA8B;MAAA;MACAX;QACAC;QACAC;UACA;YACA;YACArB;UACA;QACA;MACA;IACA;IACA+B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAZ;oBACAa;oBACAC;oBACAC;oBACAb;sBAAA;wBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACArB;gCACAmB;gCACAA;gCAAA;gCAAA;gCAAA,OAEAA;kCACA9B;gCACA;8BAAA;gCAAA;gCAAA;gCAFA8C;gCAAAC;gCAGA;kCAAA,wBAKAA,8CAHAC,2CACAC,mCACAC;kCAEAC;kCACA;oCACA1C;oCACA2C;oCACAC;kCACA;kCACAvB;oCACAwB;oCACAH;kCACA;kCACA;oCACA1C;oCACA2C;oCACAC;kCACA;kCACA1C;oCAAA2C;oCAAAH;kCAAA;gCACA;gCAAA;gCAAA;8BAAA;gCAAA;gCAAA;gCAEAxC;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CAEA;sBAAA;wBAAA;sBAAA;sBAAA;oBAAA;oBACAsB;sBACAtB;oBACA;kBACA;gBACA;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA4C;MAAA;MACA;MACA;QACA5C;QACA;MACA;MACA;QACAO;MACA;QACAP;QACA;QACA;UAAA;UACA;YACAO;YACAsC;YACAC;YACAC;YACAC;YACAN;YACAD;UACA;UACAzC;UACA;YACA;cACAA;cACA;YACA;cACA;YACA;UACA;YACA;cACA;YACA;YACA;YACA;cACAf;cACAoB;cACAC;cACAC;cACAE;YACA;YACAU;cACA0B;cACAxC;cACA4C;cACA1C;cACAC;cACAC;cACAE;cACAuC;YACA;YACA;cACAC;cACAC;YACA;YACA;YACA;cACA;YACA;UACA;QACA;UACA;YACA;cACA;YACA;YACA;YACA;cACAnE;cACAoB;cACAC;cACAC;cACAE;YACA;YACAU;cACA0B;cACAxC;cACA4C;cACA1C;cACAC;cACAC;cACAE;cACAuC;YACA;YACA;cACAC;cACAC;YACA;YACA;YACA;cACA;YACA;UACA;QACA;MACA;QACApD;QACA;QACA;UACAf;UACAoB;UACAC;UACAC;UACAE;QACA;QACAU;UACA0B;UACAxC;UACA4C;UACA1C;UACAC;UACAC;UACAE;UACAuC;QACA;QACA;UACAC;UACAC;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAtD;QACA;MACA;MACA;MACA;MACA;QACAO;QACAgD;MACA;QACAvD;QACA;UAAA,uCACAwD;YACAlE,kEACAmE,6DACAA,uDACAA,qDACAA,yDACAA;UAAA;QAAA,CACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA1D;MACAA;IACA;EAAA,GACA;IACA2D;MACA;;MAaA3D;MACA;;MAGA;MACA;MACAA;IACA;IACA4D;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAzE;MACA;QACAA;MACA;MACA8B;QACA9B;MACA;IACA;IACA0E;MAAA;MACA/D;MACA;QACA;UACAf;UACAoB;UACAC;UACAC;UACAE;QACA;QAEAT;QAEA;UACA;YACAmD;YACAC;UACA;UACA;UACApD;UACA;YACA;UACA;QACA;UACAA;UACA;QACA;MACA;IACA;IAEA;IACAgE;MAAA;MACAhE;MACA;QACAA;QACA;MACA;;MAEA;MACA0B,aACA;QACA1B;QACA;MACA,IACA;QACAA;QACA;MACA,IACA;QACAA;QACA;MACA,GACA;QACAA;QACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAiE;MACAjE;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;;MAEA;QACA;UACA;UACA;UACAA;UACA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACA;QACAmB;QACAnB;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAkE;MAAA;MACAlE;MACA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACAmB;QACAnB;QACA;QACA;MACA;QACAA;QACA;QACA;QACA;MACA;IACA;IACA;IACAmE;MAAA;MACA;;MAEA;MACA,qBACA,iBACA,sBACA,qBACA,mBACA,iBACA,0BACA,iBACA,qBACA,iBACA,qBACA;MAEA;QAAA;MAAA;QACA;MACA;MAEAhD;QACA9B;QACAiC;UACAtB;UACA;QACA;MACA;IACA;IAEAoE;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACA;MACA;QAAA;MAAA;QACA;MACA;MACAjD;QACA9B;MACA;IACA;IAEA;IACAgF;MAAA;MACAlD;QACAmD;QACAnF;QACAoF;MACA;IACA;IACAC;MACArD;MACAA;MACAA;MACAA;MACAA;IACA;IACAsD;MAAA;MACA;QACAzE;QACA;MACA;MACA;MACA,iCACA0E;QACA;QACA;UACA;QACA;QACA;UACAzF;UACAoB;UACAC;UACAC;UACAoE;UACAlE;UACApC;QACA;QACA;UACA8E;UACAC;QACA;QACApD;QACA;MACA,GACA4E;QACA5E;QACA;UACA;QACA;UACA;YACA;UAAA;QAEA;MACA,GACA6E;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA3D;MACA;MACAA;MACA;QACAgC;QACAC;MACA;MACA;QACAD;QACAC;MACA;MACA;MACA;QAAA,uCACAI;UACAlE;QAAA;MAAA,CACA;MACA;QACA;MACA;IACA;IACA;IACAyF;MACA;IACA;IACAC;MACA;MACA;QAAA/F;QAAAV;MAAA;MACA;QACA0G;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAlG;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;kBAAAA;gBAAA;cAAA;gBAAAmG;gBAEA;kBACA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEApF;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqF;MAAA;MACA;MACA;QACA;QACA;UACAJ;UACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,wBAEA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAAA,IAGA/G;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAGA;gBACA4C;kBAAAmD;gBAAA;gBAAA;gBAGA;gBACAiB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEAC,+CAEA;gBACAC;kBACAxG;kBACAyG;kBACAH;kBACAI;kBAAA;kBACAC;gBACA;;gBAEA5F;gBAAA;gBAAA,OAEA;cAAA;gBAAAoF;gBACApF;gBAAA,MAEAoF;kBAAA;kBAAA;gBAAA;gBACA;;gBAEA;gBACAS,kDACA;kBACA5G;gBAAA;gBAGA;kBACAkE;kBACAC;gBACA;;gBAEA;gBACAjC;;gBAEA;gBACA2E;gBACAA;gBACA3E;;gBAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAnB;gBACA;cAAA;gBAAA;gBAEA;gBACAmB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA4E;MAAA;MAOA;QACA;MACA;MACA;MACA;MACA5E;QACA6E;QACA1B;MACA;MACA,gBAGA2B;QAFAC;QACAC;MAEAhF;QACAE;UACA;YACA9C;YACA2H;YACAC;YACAR;YACAlF;UACA;QACA;QACAa;UACAH;YACAC;YACAC;cACA;gBACA;gBACArB;gBACA;kBACAzB;kBACA2H;kBACAC;kBACAR;kBACAlF;gBACA;cACA;gBACA;gBACAU;gBACA;cACA;YACA;YACAG;cACA;cACAH;cACA;YACA;UACA;QACA;MACA;IACA;IACAiF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACA7H;kBACA2H;kBACAC;kBACAR;kBACAlF;gBACA;cAAA;gBANA2E;gBAOApF;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEAmB;gBACA;kBACAgC;kBACAC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAiD;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEArG;gBACAsG;kBACArH;kBACAoB;kBACAC;kBACAC;kBACAE;gBACA;gBACA;kBACA0C;kBACAC;gBACA;gBACA;gBAAA;gBAAA,OACA;kBACA7C;gBACA;cAAA;gBAFAgG;gBAGAvG;gBACA;gBACAA;gBAAA,MAEAuG;kBAAA;kBAAA;gBAAA;gBACAC;kBACAjG;kBACAsC;kBACAC;kBACAC;kBACAC;kBACAN;kBACAD;gBACA;gBACAzC;gBAAA;gBAAA,OACA;cAAA;gBAAAyG;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEAzG;gBACA;gBAAA;gBAAA,OACA;kBACAO;gBACA;cAAA;gBAFAmG;gBAGA;gBACA1G;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA2G;gBACA3G;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAEA4G,6BAEA;gBACA;kBACA;kBACA5G;gBACA;kBACA;kBACAA;gBACA;gBAEAL;kBACAV;kBACAoB;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GACA;gBACAoG;kBACAhE;kBACAxC;kBACA4C;kBACAzC;kBACAD;kBACAE;kBACAE;kBACAuC;gBACA;gBAEA/B;gBACA;gBACAA;gBAEAnB;gBACAA;gBAEA;kBACAmD;kBACAC;gBACA;gBACA;gBAEA0D;gBACAC;gBACA;kBACA;kBACA5F;gBACA;gBAEA;gBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;kBACA;kBACAnB;oBACArB;oBACA+B;oBACAlC;oBACAwI;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAhH;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACAmB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EAAA,iFAIAmD;IAAA;IACAnD;MACAmD;MACAnF;MACAoF;IACA;EACA,8FACA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cAAA;cAAA,OACA;YAAA;cAAA0C;cACAC;cACAlH;cAAA,MACA;gBAAA;gBAAA;cAAA;cAAA,MACA;YAAA;cAEA;cACAL;gBACAV;gBACAoB;gBACAC;gBACAE;gBACAD;gBACAE;cACA;cACAU;gBACA0B;gBACAxC;gBACA4C;gBACA1C;gBACAC;gBACAC;gBACAE;gBACAuC;cACA;cACA;gBACAC;gBACAC;cACA;cACA0D;cACAC;cACA;gBACA;gBACA5F;cACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAnB;cACA;cACAmH;gBACAlI;gBACAoB;gBACAC;gBACAC;gBACAE;cACA;cACAU;gBACA0B;gBACAxC;gBACA4C;gBACA1C;gBACAC;gBAAA;gBACAC;gBACAE;gBACAuC;cACA;cACA;gBACAC;gBACAC;cACA;cACA0D;cACAC;cACA;gBACA;gBACA5F;cACA;cACA;gBACA;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,8FACA;IACA;IACA;IACAnB;EACA,8GACA;IACAA;IACA;MACAmB;QACAmD;QACA8C;QACAC;QACAC;QACAC;QACAlG;UACA;YACA;YACAF;cACA9B;cACAiC;gBACAtB;gBACAmB;kBACAmD;kBACAnF;gBACA;cACA;YACA;UACA;QACA;QACAmC;UACAtB;QACA;MACA;IACA;EACA,4FACAX;IACA;IAEA;MACA;MACA;IACA;;IAEA;IACA;MACA8B;QACAmD;QACAnF;MACA;MACA;MACA;IACA;IAEA;MACAgC;QACAmD;QACAnF;MACA;MACA;IACA;MACAgC;QACAmD;QACAnF;MACA;IACA;MACA;IACA;MACA;IACA;EACA,0FACA;IACA;IACA;MACAgC;QACAmD;QACAnF;MACA;MACA;MACA;IACA;IAEA;MACAgC;QACAmD;QACAnF;MACA;MACA;IACA;MACAgC;QACAmD;QACAnF;MACA;IACA;MACA;IACA;EACA,8EACA;IACAgC;MACAmD;MACAnF;IACA;EACA,kFAEAmF;IAAA;IACAnD;MACAmD;MACAnF;MACAoF;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACloDA;AAAA;AAAA;AAAA;AAAkkD,CAAgB,shDAAG,EAAC,C;;;;;;;;;;;ACAtlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/mine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mine.vue?vue&type=template&id=ef6e6e68&\"\nvar renderjs\nimport script from \"./mine.vue?vue&type=script&lang=js&\"\nexport * from \"./mine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mine.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=template&id=ef6e6e68&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"pages-mine\">\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"avatar_view\">\n\t\t\t\t\t<image mode=\"aspectFill\" class=\"avatar\"\n\t\t\t\t\t\t:src=\"displayUserInfo.avatarUrl || '/static/mine/default_user.png'\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t<view v-if=\"!isLoggedIn\">\n\t\t\t\t\t\t<button @click=\"showLoginPopup\" :disabled=\"isLoading\" :class=\"{ 'loading': isLoading }\">\n\t\t\t\t\t\t\t{{ isLoading ? '登录中...' : '用户登录' }}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-else class=\"user-info-logged\">\n\t\t\t\t\t\t<view class=\"nickname\">\n\t\t\t\t\t\t\t{{ displayUserInfo.nickName }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"displayUserInfo.phone\" class=\"phone-number\">{{ displayUserInfo.phone }}</view>\n\t\t\t\t\t\t<view v-else class=\"bind-phone-container\">\n\t\t\t\t\t\t\t<button @click=\"showBindPhonePopup\" class=\"bind-phone-btn\" :disabled=\"isBindingPhone\">\n\t\t\t\t\t\t\t\t{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"status-badge\" :class=\"statusBadgeClass\">\n\t\t\t\t\t\t\t{{ statusText }}\n\t\t\t\t\t\t\t{{ labelName }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view @click=\"navigateTo('../user/userProfile')\" class=\"settings\">\n\t\t\t\t\t<i class=\"iconfont icon-xitong text-bold\"></i>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"mine-menu-list box-shadow fill-base box1\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">我的订单</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(item, index) in orderList\" :key=\"index\" @tap=\"navigateTo(item.url)\">\n\t\t\t\t\t<view class=\"icon-container\">\n\t\t\t\t\t\t<u-icon :name=\"item.icon\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t\t<view v-if=\"item.count > 0\" class=\"number-circle\">{{ item.count }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"mine-menu-list box-shadow fill-base\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">常用功能</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" v-for=\"(item, index) in orderList3\" :key=\"index\"\n\t\t\t\t\t@tap=\"handleNavigate(item.url)\">\n\t\t\t\t\t<u-icon :name=\"item.icon\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">{{ item.text }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"mine-menu-list box-shadow fill-base\">\n\t\t\t<view class=\"menu-title flex-between pl-lg pr-md b-1px-b\">\n\t\t\t\t<view class=\"f-paragraph c-title text-bold\">其他功能</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex-warp pt-lg pb-lg\">\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/shifu/skills')\">\n\t\t\t\t\t<u-icon name=\"plus-square-fill\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">技能标签</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/shifu/Professiona')\">\n\t\t\t\t\t<u-icon name=\"order\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view class=\"mt-sm\">技能证书</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/shifu/coreWallet')\">\n\t\t\t\t\t<u-icon name=\"red-packet-fill\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t<view style=\"color: #448cfb;\" class=\"mt-sm\">提现管理</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\" @tap=\"handleNavigate('/user/promotion')\">\n\t\t\t\t\t<u-icon name=\"red-packet-fill\" color=\"#E41F19\" size=\"28\"></u-icon>\n\t\t\t\t\t<view style=\"color: #E41F19;\" class=\"mt-sm\">邀请有礼</view>\n\t\t\t\t</view>\n\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"spacer\"></view>\n\n\t\t<view class=\"mine-tool-grid fill-base\">\n\t\t\t<view class=\"grid-container\">\n\t\t\t\t<view class=\"grid-item\" v-for=\"(item, index) in toolList2\" :key=\"index\" @tap=\"handleNavigate(item.url)\">\n\t\t\t\t\t<view class=\"grid-icon-container\">\n\t\t\t\t\t\t<u-icon :name=\"item.icon\" :color=\"item.iconColor\" size=\"28\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"grid-text\">{{ item.text }}</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- <view class=\"grid-item\" @tap=\"navigateTo('../pages/service')\">\n\t\t\t\t\t<view class=\"grid-icon-container switch-identity\">\n\t\t\t\t\t\t<u-icon name=\"man-add\" color=\"#E41F19\" size=\"28\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view style=\"color: #E41F19;\" class=\"grid-text\">切换用户版</view>\n\t\t\t\t</view> -->\n\n\t\t\t\t<view class=\"grid-item\">\n\t\t\t\t\t<button class=\"contact-btn-wrapper\" open-type=\"contact\" bindcontact=\"handleContact\"\n\t\t\t\t\t\tsession-from=\"sessionFrom\">\n\t\t\t\t\t\t<view class=\"grid-icon-container switch-identity\">\n\t\t\t\t\t\t\t<u-icon name=\"server-man\" color=\"#448cfb\" size=\"28\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"grid-text\">客服</view>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- <view class=\"floating-contact\">\n\t\t\t<view class=\"contact-container\">\n\t\t\t\t<u-icon name=\"server-man\" color=\"#576b95\" size=\"24\"></u-icon>\n\t\t\t\t<button class=\"contact-btn\" open-type=\"contact\" bindcontact=\"handleContact\" session-from=\"sessionFrom\">\n\t\t\t\t\t客服\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view> -->\n\n\t\t<view v-if=\"loginPopupVisible\" class=\"login-popup-overlay\" @tap=\"hideLoginPopup\">\n\t\t\t<view class=\"login-popup\" @tap.stop>\n\t\t\t\t<view class=\"close-btn\" @tap=\"hideLoginPopup\">\n\t\t\t\t\t<i class=\"iconfont icon-close\"></i>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<view class=\"welcome-title\">欢迎登录今师傅</view>\n\t\t\t\t\t<view class=\"welcome-subtitle\">登录后即可享受完整服务</view>\n\n\t\t\t\t\t<view class=\"agreement-section\">\n\t\t\t\t\t\t<view class=\"checkbox-container\" @tap=\"toggleAgreement\">\n\t\t\t\t\t\t\t<view class=\"checkbox\" :class=\"{ 'checked': agreedToTerms }\">\n\t\t\t\t\t\t\t\t<i v-if=\"agreedToTerms\" class=\"iconfont icon-check\">✓</i>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"agreement-text\">\n\t\t\t\t\t\t\t\t我已阅读并同意 <text class=\"link\" @tap.stop=\"navigateToAgreement('service')\">《今师傅服务协议》</text>\n\t\t\t\t\t\t\t\t<text class=\"link\" @tap.stop=\"navigateToAgreement('privacy')\">《隐私政策》</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<button class=\"phone-login-btn\" :class=\"{ 'disabled': !agreedToTerms || isLoading }\"\n\t\t\t\t\t\t:disabled=\"!agreedToTerms || isLoading\" open-type=\"getPhoneNumber\"\n\t\t\t\t\t\t@getphonenumber=\"onGetPhoneNumber\">\n\t\t\t\t\t\t{{ isLoading ? '登录中...' : '手机号快捷登录' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- Bind Phone Popup -->\n\t\t<view v-if=\"bindPhonePopupVisible\" class=\"login-popup-overlay\" @tap=\"hideBindPhonePopup\">\n\t\t\t<view class=\"login-popup\" @tap.stop>\n\t\t\t\t<!-- Close Button -->\n\t\t\t\t<view class=\"close-btn\" @tap=\"hideBindPhonePopup\">\n\t\t\t\t\t<i class=\"iconfont icon-close\"></i>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- Popup Content -->\n\t\t\t\t<view class=\"popup-content\">\n\t\t\t\t\t<view class=\"welcome-title\">绑定手机号</view>\n\t\t\t\t\t<view class=\"welcome-subtitle\">绑定手机号后可享受完整服务</view>\n\n\t\t\t\t\t<!-- Phone Input -->\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"phone\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入手机号\" v-model=\"bindPhoneForm.phone\"\n\t\t\t\t\t\t\t\tmaxlength=\"11\" />\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"input-item\">\n\t\t\t\t\t\t\t<view class=\"input-icon\">\n\t\t\t\t\t\t\t\t<u-icon name=\"chat\" color=\"#3b82f6\" size=\"18\"></u-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" placeholder=\"请输入验证码\" v-model=\"bindPhoneForm.code\"\n\t\t\t\t\t\t\t\tmaxlength=\"6\" />\n\t\t\t\t\t\t\t<view class=\"sms-btn\" @click=\"sendBindPhoneSmsCode\"\n\t\t\t\t\t\t\t\t:class=\"{ disabled: bindPhoneSmsCountdown > 0 }\">\n\t\t\t\t\t\t\t\t{{ bindPhoneSmsCountdown > 0 ? `${bindPhoneSmsCountdown}s` : '获取验证码' }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- Bind Button -->\n\t\t\t\t\t<button class=\"phone-login-btn\" :class=\"{ 'disabled': !canBindPhone || isBindingPhone }\"\n\t\t\t\t\t\t:disabled=\"!canBindPhone || isBindingPhone\" @click=\"handleBindPhone\">\n\t\t\t\t\t\t{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<tabbar cur=\"1\"></tabbar>\n\t</view>\n</template>\n\n<script>\nimport tabbar from \"@/components/tabbarsf.vue\";\nimport {\n\tmapState,\n\tmapMutations\n} from \"vuex\";\n\n// Utility function for debouncing\nconst debounce = (func, wait) => {\n\tlet timeout;\n\treturn function (...args) {\n\t\tconst context = this;\n\t\tclearTimeout(timeout);\n\t\ttimeout = setTimeout(() => func.apply(context, args), wait);\n\t};\n};\n\nexport default {\n\tcomponents: {\n\t\ttabbar\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tisLoading: false, // 确保初始状态不是loading\n\t\t\tinviteCode: '',\n\t\t\ttmplIds: [\n\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\tcode: '',\n\t\t\tlabelName: '',\n\t\t\tloginPopupVisible: false,\n\t\t\tagreedToTerms: false,\n\t\t\tshifustatus: -1, // Initialize with a default numeric value\n\t\t\t// 绑定手机号相关\n\t\t\tbindPhonePopupVisible: false,\n\t\t\tisBindingPhone: false,\n\t\t\tbindPhoneSmsCountdown: 0,\n\t\t\tbindPhoneSmsTimer: null,\n\t\t\tbindPhoneForm: {\n\t\t\t\tphone: '',\n\t\t\t\tcode: ''\n\t\t\t},\n\t\t\torderList: [{\n\t\t\t\ticon: 'order',\n\t\t\t\ttext: '全部',\n\t\t\t\turl: '/shifu/master_my_order?tab=0',\n\t\t\t\tcount: 0\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'bell',\n\t\t\t\ttext: '待上门',\n\t\t\t\turl: '/shifu/master_my_order?tab=3',\n\t\t\t\tcount: 0\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'hourglass-half-fill',\n\t\t\t\ttext: '待服务',\n\t\t\t\turl: '/shifu/master_my_order?tab=5',\n\t\t\t\tcount: 0\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'clock',\n\t\t\t\ttext: '服务中',\n\t\t\t\turl: '/shifu/master_my_order?tab=6',\n\t\t\t\tcount: 0\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'thumb-up',\n\t\t\t\ttext: '已完成',\n\t\t\t\turl: '/shifu/master_my_order?tab=7',\n\t\t\t\tcount: 0\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'chat-fill',\n\t\t\t\ttext: '售后',\n\t\t\t\turl: '/shifu/master_my_order?tab=8',\n\t\t\t\tcount: 0\n\t\t\t},\n\t\t\t],\n\t\t\torderList3: [{\n\t\t\t\ticon: 'red-packet',\n\t\t\t\ttext: '服务收入',\n\t\t\t\turl: '/shifu/income'\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'file-text-fill',\n\t\t\t\ttext: '报价列表',\n\t\t\t\turl: '/shifu/master_bao_list'\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'rmb-circle',\n\t\t\t\ttext: '保证金',\n\t\t\t\turl: '/shifu/Margin'\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'rmb-circle',\n\t\t\t\ttext: '师傅等级',\n\t\t\t\turl: '/shifu/shifuGrade'\n\t\t\t}\n\t\t\t],\n\t\t\ttoolList2: [{\n\t\t\t\ticon: 'plus-people-fill',\n\t\t\t\ttext: '师傅入驻',\n\t\t\t\turl: '/shifu/Settle',\n\t\t\t\ticonColor: '#448cfb'\n\t\t\t},\n\t\t\t{\n\t\t\t\ticon: 'edit-pen',\n\t\t\t\ttext: '编辑师傅资料',\n\t\t\t\turl: '/shifu/master_Info',\n\t\t\t\ticonColor: '#448cfb'\n\t\t\t}\n\t\t\t]\n\t\t};\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\t// Changed from storeUserInfo to userInfo based on user's request and Vuex module\n\t\t\tuserInfo: state => state.user.userInfo || {},\n\t\t\ttoken: state => state.user.autograph || '',\n\t\t\terweima: state => state.user.erweima || '',\n\t\t\tregeocode: (state) => state.service.regeocode,\n\t\t}),\n\t\tisLoggedIn() {\n\t\t\t// 检查是否有token，以及是否有用户ID或手机号\n\t\t\tconst hasToken = !!this.token;\n\t\t\tconst hasUserInfo = !!(this.userInfo.userId || this.userInfo.phone || uni.getStorageSync('userId') || uni.getStorageSync('phone'));\n\n\t\t\t// 只在调试模式下打印日志，避免过多的控制台输出\n\t\t\tif (process.env.NODE_ENV === 'development') {\n\t\t\t\tconsole.log('isLoggedIn检查:', {\n\t\t\t\t\thasToken,\n\t\t\t\t\thasUserInfo,\n\t\t\t\t\tresult: hasToken && hasUserInfo\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn hasToken && hasUserInfo;\n\t\t},\n\t\t// This computed property will combine Vuex userInfo with local storage `shiInfo`\n\t\t// for display purposes, giving `shiInfo` priority where it makes sense.\n\t\tdisplayUserInfo() {\n\t\t\tconst shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};\n\n\t\t\t// 优先使用有效的师傅信息，如果师傅信息为空则使用用户信息\n\t\t\tconst avatarUrl = (shiInfo.avatarUrl && shiInfo.avatarUrl.trim()) ? shiInfo.avatarUrl :\n\t\t\t\t(this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png');\n\n\t\t\tconst nickName = (shiInfo.coachName && shiInfo.coachName.trim()) ? shiInfo.coachName :\n\t\t\t\t(this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户');\n\n\t\t\tconst phone = (shiInfo.mobile && shiInfo.mobile.trim()) ? shiInfo.mobile :\n\t\t\t\t(this.userInfo.phone || uni.getStorageSync('phone') || '');\n\n\t\t\tconst userId = shiInfo.userId || this.userInfo.userId || uni.getStorageSync('userId') || '';\n\t\t\tconst pid = shiInfo.pid || this.userInfo.pid || uni.getStorageSync('pid') || '';\n\t\t\tconst shifuId = shiInfo.shifuId || '';\n\n\t\t\tconst result = {\n\t\t\t\tphone: this.isLoggedIn ? phone : '',\n\t\t\t\tavatarUrl: this.isLoggedIn ? avatarUrl : '/static/mine/default_user.png',\n\t\t\t\tnickName: this.isLoggedIn ? nickName : '微信用户',\n\t\t\t\tuserId: this.isLoggedIn ? userId : '',\n\t\t\t\tshifuId: this.isLoggedIn ? shifuId : '',\n\t\t\t\tpid: this.isLoggedIn ? pid : ''\n\t\t\t};\n\n\t\t\tconsole.log('displayUserInfo计算结果:', result);\n\t\t\treturn result;\n\t\t},\n\t\tstatusText() {\n\t\t\t// 如果用户未登录，不显示状态信息\n\t\t\tif (!this.isLoggedIn) {\n\t\t\t\treturn '';\n\t\t\t}\n\n\t\t\t// 优先从本地缓存获取状态，提高响应速度\n\t\t\tconst cachedShiInfo = uni.getStorageSync('shiInfo');\n\t\t\tlet status = this.shifustatus;\n\n\t\t\tif (cachedShiInfo) {\n\t\t\t\ttry {\n\t\t\t\t\tconst parsedShiInfo = JSON.parse(cachedShiInfo);\n\t\t\t\t\tif (parsedShiInfo.status !== undefined && parsedShiInfo.status !== null) {\n\t\t\t\t\t\tstatus = parsedShiInfo.status;\n\t\t\t\t\t\t// 如果本地状态与当前状态不一致，更新当前状态\n\t\t\t\t\t\tif (this.shifustatus !== status) {\n\t\t\t\t\t\t\tthis.shifustatus = status;\n\t\t\t\t\t\t\tconsole.log('从缓存同步师傅状态:', status);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('解析本地师傅信息失败:', error);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 确保状态是数字类型\n\t\t\tstatus = Number(status);\n\n\t\t\t// 如果状态仍然无效，设置默认状态并触发获取（仅在已登录时）\n\t\t\tif (isNaN(status) || status === undefined || status === null) {\n\t\t\t\tconsole.log('检测到无效状态，设置为默认状态');\n\t\t\t\tstatus = -1; // 默认为未入驻\n\n\t\t\t\t// 只有在已登录时才触发获取师傅信息\n\t\t\t\tif (this.isLoggedIn && !this._fetchingStatus) {\n\t\t\t\t\tconsole.log('用户已登录，触发获取师傅信息');\n\t\t\t\t\tthis._fetchingStatus = true;\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.fetchShifuInfoImmediately().finally(() => {\n\t\t\t\t\t\t\tthis._fetchingStatus = false;\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tswitch (status) {\n\t\t\t\tcase -1:\n\t\t\t\t\treturn '未入驻师傅';\n\t\t\t\tcase 1:\n\t\t\t\t\treturn '审核中';\n\t\t\t\tcase 2:\n\t\t\t\t\treturn '已认证';\n\t\t\t\tcase 4:\n\t\t\t\t\treturn '审核驳回';\n\t\t\t\tdefault:\n\t\t\t\t\tconsole.log('未知的师傅状态值:', status);\n\t\t\t\t\treturn '未入驻师傅'; // 默认显示未入驻\n\t\t\t}\n\t\t},\n\t\tstatusBadgeClass() {\n\t\t\treturn {\n\t\t\t\t'status-not-registered': this.shifustatus === -1,\n\t\t\t\t'status-pending': this.shifustatus === 1,\n\t\t\t\t'status-approved': this.shifustatus === 2,\n\t\t\t\t'status-rejected': this.shifustatus === 4\n\t\t\t};\n\t\t},\n\t\tcanBindPhone() {\n\t\t\treturn this.bindPhoneForm.phone && this.bindPhoneForm.code &&\n\t\t\t\tthis.validatePhone(this.bindPhoneForm.phone);\n\t\t}\n\t},\n\twatch: {\n\t\t// Watch for changes in the Vuex userInfo and trigger updates\n\t\tuserInfo: {\n\t\t\thandler(newVal, oldVal) {\n\t\t\t\t// Only update if there's a meaningful change to avoid infinite loops\n\t\t\t\tif (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {\n\t\t\t\t\tthis.saveUserInfoToStorage(newVal);\n\t\t\t\t\t// Force update if necessary, though Vue's reactivity should handle most cases\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tdeep: true, // Watch for nested property changes\n\t\t\timmediate: true // Run the handler immediately on component mount\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.getNowPosition();\n\t\t// 先从缓存加载 labelName，避免显示延迟\n\t\tthis.loadCachedLabelName();\n\t\tthis.getmyGrade();\n\t\tif (options.inviteCode) {\n\t\t\tconsole.log('Received inviteCode:', options.inviteCode);\n\t\t\tthis.inviteCode = options.inviteCode;\n\t\t\tuni.setStorageSync('receivedInviteCode', options.inviteCode);\n\t\t}\n\t\tif (this.erweima) {\n\t\t\tconsole.log('erweima from Vuex:', this.erweima);\n\t\t\tthis.inviteCode = this.erweima;\n\t\t\tuni.setStorageSync('receivedInviteCode', this.erweima);\n\t\t} else {\n\t\t\tconst erweima = uni.getStorageSync('erweima');\n\t\t\tif (erweima) {\n\t\t\t\tconsole.log('erweima from storage:', erweima);\n\t\t\t\tthis.$store.commit('setErweima', erweima);\n\t\t\t\tthis.inviteCode = erweima;\n\t\t\t\tuni.setStorageSync('receivedInviteCode', erweima);\n\t\t\t}\n\t\t}\n\n\t\t// 初始化用户数据\n\t\tthis.initUserData();\n\t\t// #ifdef MP-WEIXIN\n\t\tuni.login({\n\t\t\tprovider: 'weixin',\n\t\t\tsuccess: res => {\n\t\t\t\tif (res.code) {\n\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\tconsole.log('Initial wx.login code:', this.code);\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: err => {\n\t\t\t\tconsole.error('wx.login failed:', err);\n\t\t\t}\n\n\t\t});\n\t\t// #endif\n\t\t// 只有在确认有token时才进行后续操作，避免不必要的loading状态\n\t\tif (this.token) {\n\t\t\tif (this.isLoggedIn) {\n\t\t\t\tthis.debounceGetHighlight();\n\t\t\t}\n\t\t\t// 延迟获取师傅信息，确保用户数据已经加载完成\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.fetchShifuInfo();\n\t\t\t});\n\t\t}\n\t},\n\tonShow() {\n\t\tconsole.log('=== Mine页面onShow调试信息 ===');\n\t\tconsole.log('token:', this.token);\n\t\tconsole.log('isLoggedIn:', this.isLoggedIn);\n\t\tconsole.log('userInfo from Vuex:', this.userInfo);\n\t\tconsole.log('shiInfo from storage:', uni.getStorageSync('shiInfo'));\n\t\tconsole.log('displayUserInfo:', this.displayUserInfo);\n\n\t\t// 确保初始状态不是loading\n\t\tthis.isLoading = false;\n\n\t\t// 每次显示页面时都先加载缓存的 labelName\n\t\tthis.loadCachedLabelName();\n\n\t\t// 如果没有token，直接清除登录状态\n\t\tif (!this.token) {\n\t\t\tconsole.log('没有token，清除登录状态');\n\t\t\tthis.handleInvalidSession();\n\t\t\treturn;\n\t\t}\n\n\t\t// 如果有token但用户信息不完整，尝试初始化用户数据\n\t\tif (!this.isLoggedIn) {\n\t\t\tconsole.log('有token但用户信息未完整加载，尝试初始化用户数据');\n\t\t\tthis.initUserData();\n\t\t\t// 给一点时间让数据初始化完成，但不显示loading状态\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (this.isLoggedIn) {\n\t\t\t\t\tthis.loadUserRelatedData();\n\t\t\t\t}\n\t\t\t}, 100);\n\t\t} else {\n\t\t\t// 已登录状态，直接加载用户相关数据\n\t\t\tthis.loadUserRelatedData();\n\t\t}\n\n\t\tthis.$nextTick(() => {\n\t\t\tthis.$forceUpdate();\n\t\t});\n\t},\n\tonPullDownRefresh() {\n\t\t// Handle pull-down refresh\n\t\tif (this.isLoggedIn && this.token) {\n\t\t\tPromise.all([\n\t\t\t\tthis.fetchUserInfo(),\n\t\t\t\tthis.getHighlight(),\n\t\t\t\tthis.fetchShifuInfo(),\n\t\t\t\tthis.getmyGrade() // 添加师傅等级刷新\n\t\t\t]).then(() => {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t// this.showToast('刷新成功', 'success');\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('Pull-down refresh failed:', err);\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t} else {\n\t\t\t// If not logged in, reset UI and stop refresh\n\t\t\tthis.handleInvalidSession();\n\t\t\tuni.stopPullDownRefresh();\n\t\t\tthis.showToast('请先登录');\n\t\t}\n\t},\n\tmethods: {\n\t\t// 从缓存加载 labelName，避免显示延迟\n\t\tloadCachedLabelName() {\n\t\t\tconst cachedLabelName = uni.getStorageSync('labelName');\n\t\t\tif (cachedLabelName) {\n\t\t\t\tthis.labelName = cachedLabelName;\n\t\t\t\tconsole.log('从缓存加载 labelName:', cachedLabelName);\n\t\t\t}\n\t\t},\n\n\t\tgetmyGrade() {\n\t\t\t// 如果已经有缓存的 labelName，就不显示加载状态\n\t\t\tconst hasCache = !!uni.getStorageSync('labelName');\n\n\t\t\tthis.$api.shifu.getGrade().then(res => {\n\t\t\t\tconsole.log('getGrade response:', res);\n\t\t\t\tif (res && res.data && res.data.labelName) {\n\t\t\t\t\tthis.labelName = res.data.labelName;\n\t\t\t\t\t// 缓存 labelName，下次可以立即显示\n\t\t\t\t\tuni.setStorageSync('labelName', res.data.labelName);\n\t\t\t\t\tconsole.log('更新并缓存 labelName:', res.data.labelName);\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('获取师傅等级失败:', err);\n\t\t\t\t// 如果接口失败，保持使用缓存的值\n\t\t\t\tif (!this.labelName) {\n\t\t\t\t\tthis.labelName = uni.getStorageSync('labelName') || '';\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tgetmylogin() {\n\t\t\tuni.login({\n\t\t\t\tprovider: 'weixin',\n\t\t\t\tsuccess: res => {\n\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\tconsole.log('Initial wx.login code:', this.code);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tasync getNowPosition() {\n\t\t\ttry {\n\t\t\t\tuni.getLocation({\n\t\t\t\t\ttype: \"gcj02\",\n\t\t\t\t\tisHighAccuracy: true,\n\t\t\t\t\taccuracy: \"best\",\n\t\t\t\t\tsuccess: async (locationRes) => {\n\t\t\t\t\t\tconsole.log(\"Location success:\", locationRes);\n\t\t\t\t\t\tuni.setStorageSync(\"lat\", locationRes.latitude);\n\t\t\t\t\t\tuni.setStorageSync(\"lng\", locationRes.longitude);\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst [requestError, amapResponse] = await uni.request({\n\t\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?key=2036e9b214b103fcb49c00a23de129e3&location=${locationRes.longitude},${locationRes.latitude}`,\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tif (amapResponse && amapResponse.data && amapResponse.data.regeocode) {\n\t\t\t\t\t\t\t\tconst {\n\t\t\t\t\t\t\t\t\tprovince,\n\t\t\t\t\t\t\t\t\tcity,\n\t\t\t\t\t\t\t\t\tadcode\n\t\t\t\t\t\t\t\t} = amapResponse.data.regeocode.addressComponent;\n\t\t\t\t\t\t\t\tconst position = typeof city === \"string\" ? city : province;\n\t\t\t\t\t\t\t\tthis.setRegeocode({\n\t\t\t\t\t\t\t\t\tregeocode: amapResponse.data.regeocode,\n\t\t\t\t\t\t\t\t\tlat: locationRes.latitude,\n\t\t\t\t\t\t\t\t\tlng: locationRes.longitude\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tuni.setStorageSync(\"city\", {\n\t\t\t\t\t\t\t\t\tcity_id: adcode,\n\t\t\t\t\t\t\t\t\tposition: position\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tthis.$store.dispatch('setRegeocode', {\n\t\t\t\t\t\t\t\t\tregeocode: amapResponse.data.regeocode,\n\t\t\t\t\t\t\t\t\tlat: locationRes.latitude,\n\t\t\t\t\t\t\t\t\tlng: locationRes.longitude\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tconsole.log(\"逆地理编码成功，城市信息:\", { city_id: adcode, position: position });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (regeoError) {\n\t\t\t\t\t\t\tconsole.error('Reverse geocoding failed:', regeoError);\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('Get location failed:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} catch (outerError) {\n\t\t\t\tconsole.error('Get location outer error:', outerError);\n\t\t\t}\n\t\t},\n\t\tgetshifuinfo() {\n\t\t\tconst userId = this.userInfo.userId || uni.getStorageSync('userId');\n\t\t\tif (!userId) {\n\t\t\t\tconsole.log('No userId, skipping getshifuinfo');\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\t\t\treturn this.$api.shifu.getshifstutas({\n\t\t\t\tuserId: userId\n\t\t\t}).then(res => {\n\t\t\t\tconsole.log('getshifstutas response:', res);\n\t\t\t\tthis.shifustatus = Number(res.data) !== undefined ? Number(res.data) : -1;\n\t\t\t\tif (res.data === -1) {\n\t\t\t\t\tconst userinster = {\n\t\t\t\t\t\tuserId: this.userInfo.userId || uni.getStorageSync('userId'),\n\t\t\t\t\t\tmobile: this.userInfo.phone || uni.getStorageSync('phone'),\n\t\t\t\t\t\taddress: this.regeocode?.regeocode?.formatted_address || '',\n\t\t\t\t\t\tcityId: '',\n\t\t\t\t\t\tlabelId: 25,\n\t\t\t\t\t\tlng: this.regeocode?.lng || uni.getStorageSync('lng') || 0,\n\t\t\t\t\t\tlat: this.regeocode?.lat || uni.getStorageSync('lat') || 0,\n\t\t\t\t\t};\n\t\t\t\t\tconsole.log('Registering master with:', userinster);\n\t\t\t\t\treturn this.$api.shifu.masterEnter(userinster).then(res => {\n\t\t\t\t\t\tif (res.code === \"200\") {\n\t\t\t\t\t\t\tconsole.log('Master registration successful');\n\t\t\t\t\t\t\treturn this.$api.shifu.getMaster();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrow new Error('Master registration failed');\n\t\t\t\t\t\t}\n\t\t\t\t\t}).then(masterRess => {\n\t\t\t\t\t\tif (!masterRess || typeof masterRess !== 'object') {\n\t\t\t\t\t\t\tthrow new Error('获取师傅信息失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlet masterRes = masterRess.data\n\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\tphone: masterRes.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\t\t\tavatarUrl: masterRes.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\t\tnickName: masterRes.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\t\tuserId: masterRes.id || this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\t\tpid: masterRes.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t\t\t};\n\t\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\t\t\tuserId: userInfo.userId,\n\t\t\t\t\t\t\tshifuId: masterRes.id || '',\n\t\t\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\t\t\tmessagePush: Number(masterRes.messagePush) || -1\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\treturn this.$api.shifu.getMaster().then(masterRess => {\n\t\t\t\t\t\tif (!masterRess || typeof masterRess !== 'object') {\n\t\t\t\t\t\t\tthrow new Error('获取师傅信息失败');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlet masterRes = masterRess.data\n\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\tphone: masterRes.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\t\t\tavatarUrl: masterRes.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\t\t\tnickName: masterRes.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\t\t\tuserId: masterRes.id || this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\t\t\tpid: masterRes.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t\t\t};\n\t\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\t\t\tuserId: userInfo.userId,\n\t\t\t\t\t\t\tshifuId: masterRes.id || '',\n\t\t\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\t\t\tmessagePush: Number(masterRes.messagePush) || -1\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('getshifuinfo error:', err);\n\t\t\t\tthis.shifustatus = -1;\n\t\t\t\tconst defaultUserInfo = {\n\t\t\t\t\tphone: this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\tavatarUrl: this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\tuserId: this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\tpid: this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t};\n\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\tmobile: defaultUserInfo.phone,\n\t\t\t\t\tavatarUrl: defaultUserInfo.avatarUrl,\n\t\t\t\t\tcoachName: defaultUserInfo.nickName,\n\t\t\t\t\tuserId: defaultUserInfo.userId,\n\t\t\t\t\tshifuId: '',\n\t\t\t\t\tpid: defaultUserInfo.pid,\n\t\t\t\t\tstatus: -1,\n\t\t\t\t\tmessagePush: -1\n\t\t\t\t}));\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: defaultUserInfo\n\t\t\t\t});\n\t\t\t\tthis.saveUserInfoToStorage(defaultUserInfo);\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\tdebounceGetHighlight: debounce(function () {\n\t\t\tthis.getHighlight();\n\t\t}, 300),\n\t\tgetHighlight() {\n\t\t\tconst userId = this.userInfo.userId || uni.getStorageSync('userId');\n\t\t\tif (!userId || !this.token || !this.isLoggedIn) {\n\t\t\t\tconsole.log('No userId, token, or not logged in, skipping getHighlight');\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\t\t\t// 确保只有在已登录状态下才显示loading\n\t\t\tthis.isLoading = true;\n\t\t\treturn this.$api.service.getHighlight({\n\t\t\t\tuserId: userId,\n\t\t\t\trole: 1\n\t\t\t}).then(res => {\n\t\t\t\tconsole.log('getHighlight response:', res);\n\t\t\t\tconst updatedOrderList = this.orderList.map((item, index) => ({\n\t\t\t\t\t...item,\n\t\t\t\t\tcount: index === 0 ? (res && res.countOrder ? res.countOrder : 0) :\n\t\t\t\t\t\tindex === 1 ? (res && res.shiFuBaoJia ? res.shiFuBaoJia : 0) :\n\t\t\t\t\t\t\tindex === 2 ? (res && res.daiZhiFu ? res.daiZhiFu : 0) :\n\t\t\t\t\t\t\t\tindex === 3 ? (res && res.daiFuWu ? res.daiFuWu : 0) :\n\t\t\t\t\t\t\t\t\tindex === 4 ? (res && res.fuWuZhong ? res.fuWuZhong : 0) :\n\t\t\t\t\t\t\t\t\t\tindex === 5 ? (res && res.yiWanCheng ? res.yiWanCheng : 0) : 0\n\t\t\t\t}));\n\t\t\t\tthis.$set(this, 'orderList', updatedOrderList);\n\t\t\t}).finally(() => {\n\t\t\t\tthis.isLoading = false;\n\t\t\t});\n\t\t},\n\t\thandleContact(e) {\n\t\t\tconsole.log(e.detail.path);\n\t\t\tconsole.log(e.detail.query);\n\t\t},\n\t\t...mapMutations(['updateUserItem', 'setRegeocode']),\n\t\tshowLoginPopup() {\n\t\t\t// 检查当前运行环境\n\t\t\t// #ifdef APP-PLUS\n\t\t\tconsole.log('APP端跳转到登录页面');\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/login',\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('跳转登录页面失败:', err);\n\t\t\t\t\tthis.showToast('跳转失败，请重试');\n\t\t\t\t}\n\t\t\t});\n\t\t\t// #endif\n\n\t\t\t// #ifndef APP-PLUS\n\t\t\tconsole.log('非APP端显示登录弹窗');\n\t\t\tthis.loginPopupVisible = true;\n\t\t\t// #endif\n\n\t\t\t// 运行时检查作为备用方案\n\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\tconsole.log('当前平台:', systemInfo.platform, systemInfo.app);\n\t\t},\n\t\thideLoginPopup() {\n\t\t\tthis.loginPopupVisible = false;\n\t\t\tthis.agreedToTerms = false;\n\t\t},\n\t\ttoggleAgreement() {\n\t\t\tthis.agreedToTerms = !this.agreedToTerms;\n\t\t},\n\t\tnavigateToAgreement(type) {\n\t\t\tlet url = '../user/configuser';\n\t\t\tif (type === 'service') {\n\t\t\t\turl += '?type=service';\n\t\t\t} else if (type === 'privacy') {\n\t\t\t\turl += '?type=privacy';\n\t\t\t}\n\t\t\tuni.navigateTo({\n\t\t\t\turl: url\n\t\t\t});\n\t\t},\n\t\tinitUserData() {\n\t\t\tconsole.log('=== 初始化用户数据 ===');\n\t\t\tif (this.token && (!this.userInfo.userId && !this.userInfo.phone)) {\n\t\t\t\tconst userInfo = {\n\t\t\t\t\tphone: uni.getStorageSync('phone') || '',\n\t\t\t\t\tavatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\tuserId: uni.getStorageSync('userId') || '',\n\t\t\t\t\tpid: uni.getStorageSync('pid') || ''\n\t\t\t\t};\n\n\t\t\t\tconsole.log('从本地存储获取的用户信息:', userInfo);\n\n\t\t\t\tif (userInfo.userId || userInfo.phone) {\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t});\n\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t\tconsole.log('用户数据初始化成功');\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('本地存储中没有有效的用户数据，清除登录状态');\n\t\t\t\t\tthis.handleInvalidSession();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 加载用户相关数据的方法\n\t\tloadUserRelatedData() {\n\t\t\tconsole.log('=== 开始加载用户相关数据 ===');\n\t\t\tif (!this.isLoggedIn || !this.token) {\n\t\t\t\tconsole.log('用户未登录，跳过数据加载');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 并行加载多个数据，包括师傅等级信息\n\t\t\tPromise.all([\n\t\t\t\tthis.getHighlight().catch(err => {\n\t\t\t\t\tconsole.error('获取订单统计失败:', err);\n\t\t\t\t\treturn null;\n\t\t\t\t}),\n\t\t\t\tthis.fetchShifuInfo().catch(err => {\n\t\t\t\t\tconsole.error('获取师傅信息失败:', err);\n\t\t\t\t\treturn null;\n\t\t\t\t}),\n\t\t\t\tthis.getmyGrade().catch(err => {\n\t\t\t\t\tconsole.error('获取师傅等级失败:', err);\n\t\t\t\t\treturn null;\n\t\t\t\t})\n\t\t\t]).then(() => {\n\t\t\t\tconsole.log('用户相关数据加载完成');\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t});\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('加载用户数据时发生错误:', err);\n\t\t\t});\n\t\t},\n\n\t\t// 获取师傅信息的方法（优化版本）\n\t\tfetchShifuInfo() {\n\t\t\tconsole.log('=== 开始获取师傅信息 ===');\n\t\t\tconst userId = this.userInfo.userId || uni.getStorageSync('userId');\n\t\t\tif (!userId) {\n\t\t\t\tconsole.log('没有userId，跳过师傅信息获取');\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\n\t\t\t// 先检查本地缓存，如果有最近的数据就直接使用\n\t\t\tconst cachedShiInfo = uni.getStorageSync('shiInfo');\n\t\t\tconst cacheTimestamp = uni.getStorageSync('shiInfoTimestamp');\n\t\t\tconst now = Date.now();\n\t\t\tconst cacheExpiry = 5 * 60 * 1000; // 5分钟缓存\n\n\t\t\tif (cachedShiInfo && cacheTimestamp && (now - cacheTimestamp < cacheExpiry)) {\n\t\t\t\ttry {\n\t\t\t\t\tconst parsedShiInfo = JSON.parse(cachedShiInfo);\n\t\t\t\t\tthis.shifustatus = parsedShiInfo.status || -1;\n\t\t\t\t\tconsole.log('使用缓存的师傅信息:', parsedShiInfo.status);\n\t\t\t\t\treturn Promise.resolve();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('解析缓存的师傅信息失败:', error);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// 缓存过期或不存在，重新获取\n\t\t\treturn this.getshifuinfo().then(() => {\n\t\t\t\t// 更新缓存时间戳\n\t\t\t\tuni.setStorageSync('shiInfoTimestamp', now);\n\t\t\t\tconsole.log('师傅信息获取完成并更新缓存');\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('获取师傅信息失败:', err);\n\t\t\t\t// 即使失败也不影响页面显示\n\t\t\t});\n\t\t},\n\n\t\t// 立即获取师傅信息的方法（用于解决未知状态问题）\n\t\tfetchShifuInfoImmediately() {\n\t\t\tconsole.log('=== 立即获取师傅信息 ===');\n\t\t\tconst userId = this.userInfo.userId || uni.getStorageSync('userId');\n\t\t\tif (!userId) {\n\t\t\t\tconsole.log('没有userId，无法立即获取师傅信息');\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\n\t\t\t// 强制重新获取，不使用缓存\n\t\t\treturn this.getshifuinfo().then(() => {\n\t\t\t\t// 更新缓存时间戳\n\t\t\t\tuni.setStorageSync('shiInfoTimestamp', Date.now());\n\t\t\t\tconsole.log('立即获取师傅信息完成，状态:', this.shifustatus);\n\t\t\t\t// 强制更新界面\n\t\t\t\tthis.$forceUpdate();\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('立即获取师傅信息失败:', err);\n\t\t\t\t// 设置默认状态\n\t\t\t\tthis.shifustatus = -1;\n\t\t\t\tthis.$forceUpdate();\n\t\t\t});\n\t\t},\n\t\t// 添加缺失的 handleNavigate 方法\n\t\thandleNavigate(url) {\n\t\t\tif (!url) return;\n\n\t\t\t// 检查是否需要登录\n\t\t\tconst requiresLogin = [\n\t\t\t\t'/shifu/skills',\n\t\t\t\t'/shifu/Professiona',\n\t\t\t\t'/shifu/coreWallet',\n\t\t\t\t'/user/promotion',\n\t\t\t\t'/shifu/income',\n\t\t\t\t'/shifu/master_bao_list',\n\t\t\t\t'/shifu/Margin',\n\t\t\t\t'/shifu/shifuGrade',\n\t\t\t\t'/shifu/Settle',\n\t\t\t\t'/shifu/master_Info'\n\t\t\t];\n\n\t\t\tif (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {\n\t\t\t\treturn this.showToast('请先登录');\n\t\t\t}\n\n\t\t\tuni.navigateTo({\n\t\t\t\turl,\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('页面跳转失败:', err);\n\t\t\t\t\tthis.showToast('页面跳转失败，请重试');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\tnavigateTo(url) {\n\t\t\tif (!url) return;\n\t\t\tconst requiresLogin = [\n\t\t\t\t// '../user/coupon', // These were commented out in the original code\n\t\t\t\t// '../user/repair_record',\n\t\t\t\t// '../user/order_list',\n\t\t\t\t// '../user/address',\n\t\t\t\t// '../user/Settle',\n\t\t\t\t// '../user/agent_apply',\n\t\t\t\t// '../user/promotion',\n\t\t\t\t// '../user/bankCard',\n\t\t\t\t// '../shifu/Settle',\n\t\t\t\t// '../shifu/Receiving',\n\t\t\t\t// '../shifu/mine'\n\t\t\t];\n\t\t\tif (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {\n\t\t\t\treturn this.showToast('请先登录');\n\t\t\t}\n\t\t\tuni.navigateTo({\n\t\t\t\turl\n\t\t\t});\n\t\t},\n\n\t\t// 添加缺失的 showToast 方法\n\t\tshowToast(message, icon = 'none') {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: message,\n\t\t\t\ticon: icon,\n\t\t\t\tduration: 2000\n\t\t\t});\n\t\t},\n\t\tsaveUserInfoToStorage(userInfo) {\n\t\t\tuni.setStorageSync('phone', userInfo.phone || '');\n\t\t\tuni.setStorageSync('avatarUrl', userInfo.avatarUrl || '');\n\t\t\tuni.setStorageSync('nickName', userInfo.nickName || '');\n\t\t\tuni.setStorageSync('userId', userInfo.userId || '');\n\t\t\tuni.setStorageSync('pid', userInfo.pid || '');\n\t\t},\n\t\tfetchUserInfo() {\n\t\t\tif (this.isLoading || !this.token) {\n\t\t\t\tconsole.log('Skipping fetchUserInfo: no token or already loading');\n\t\t\t\treturn Promise.resolve();\n\t\t\t}\n\t\t\tthis.isLoading = true;\n\t\t\treturn this.$api.user.userInfo()\n\t\t\t\t.then(responses => {\n\t\t\t\t\tlet response = responses.data\n\t\t\t\t\tif (!response || typeof response !== 'object') {\n\t\t\t\t\t\tthrow new Error('获取用户信息失败: 响应数据无效');\n\t\t\t\t\t}\n\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\tphone: response.data.phone || '',\n\t\t\t\t\t\tavatarUrl: response.data.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\t\tnickName: response.data.nickName || '微信用户',\n\t\t\t\t\t\tuserId: response.data.id || '',\n\t\t\t\t\t\tcreateTime: response.data.createTime || '',\n\t\t\t\t\t\tpid: response.data.pid || '',\n\t\t\t\t\t\tinviteCode: response.data.inviteCode || ''\n\t\t\t\t\t};\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: userInfo\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log(userInfo)\n\t\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\t\t\t\t})\n\t\t\t\t.catch(error => {\n\t\t\t\t\tconsole.error('获取用户信息失败:', error);\n\t\t\t\t\tif (error.message && error.message.includes('响应数据无效')) {\n\t\t\t\t\t\tthis.handleInvalidSession();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (this.token) {\n\t\t\t\t\t\t\t// this.showToast('获取用户信息失败，请稍后重试');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t.finally(() => {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t});\n\t\t},\n\t\thandleInvalidSession() {\n\t\t\t['token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid'].forEach(key => {\n\t\t\t\tuni.removeStorageSync(key);\n\t\t\t});\n\t\t\tuni.removeStorageSync('shiInfo');\n\t\t\tthis.updateUserItem({\n\t\t\t\tkey: 'userInfo',\n\t\t\t\tval: {}\n\t\t\t});\n\t\t\tthis.updateUserItem({\n\t\t\t\tkey: 'autograph',\n\t\t\t\tval: ''\n\t\t\t});\n\t\t\tthis.isLoading = false;\n\t\t\tthis.$set(this, 'orderList', this.orderList.map(item => ({\n\t\t\t\t...item,\n\t\t\t\tcount: 0\n\t\t\t})));\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.$forceUpdate();\n\t\t\t});\n\t\t},\n\t\t// 绑定手机号相关方法\n\t\tshowBindPhonePopup() {\n\t\t\tthis.bindPhonePopupVisible = true;\n\t\t},\n\t\thideBindPhonePopup() {\n\t\t\tthis.bindPhonePopupVisible = false;\n\t\t\tthis.bindPhoneForm = { phone: '', code: '' };\n\t\t\tif (this.bindPhoneSmsTimer) {\n\t\t\t\tclearInterval(this.bindPhoneSmsTimer);\n\t\t\t\tthis.bindPhoneSmsTimer = null;\n\t\t\t\tthis.bindPhoneSmsCountdown = 0;\n\t\t\t}\n\t\t},\n\n\t\t// 验证手机号\n\t\tvalidatePhone(phone) {\n\t\t\tconst phoneReg = /^1[3-9]\\d{9}$/;\n\t\t\treturn phoneReg.test(phone);\n\t\t},\n\n\t\t// 发送绑定手机号验证码\n\t\tasync sendBindPhoneSmsCode() {\n\t\t\tif (this.bindPhoneSmsCountdown > 0) return;\n\n\t\t\tconst phone = this.bindPhoneForm.phone;\n\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\treturn this.showToast('请输入正确的手机号');\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// 调用发送验证码接口\n\t\t\t\tconst response = await this.$api.base.sendSmsCode({ phone });\n\n\t\t\t\tif (response.code === '200') {\n\t\t\t\t\tthis.showToast('验证码发送成功', 'success');\n\t\t\t\t\tthis.startBindPhoneCountdown();\n\t\t\t\t} else {\n\t\t\t\t\tthis.showToast(response.msg || '验证码发送失败，请重试');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('发送验证码失败:', error);\n\t\t\t\tthis.showToast('验证码发送失败，请重试');\n\t\t\t}\n\t\t},\n\n\t\t// 开始绑定手机号倒计时\n\t\tstartBindPhoneCountdown() {\n\t\t\tthis.bindPhoneSmsCountdown = 60;\n\t\t\tthis.bindPhoneSmsTimer = setInterval(() => {\n\t\t\t\tthis.bindPhoneSmsCountdown--;\n\t\t\t\tif (this.bindPhoneSmsCountdown <= 0) {\n\t\t\t\t\tclearInterval(this.bindPhoneSmsTimer);\n\t\t\t\t\tthis.bindPhoneSmsTimer = null;\n\t\t\t\t}\n\t\t\t}, 1000);\n\t\t},\n\n\t\t// 处理绑定手机号\n\t\tasync handleBindPhone() {\n\t\t\tif (!this.canBindPhone || this.isBindingPhone) return;\n\n\t\t\tconst { phone, code } = this.bindPhoneForm;\n\n\t\t\tif (!this.validatePhone(phone)) {\n\t\t\t\treturn this.showToast('请输入正确的手机号');\n\t\t\t}\n\n\t\t\tif (!code) {\n\t\t\t\treturn this.showToast('请输入验证码');\n\t\t\t}\n\n\t\t\tthis.isBindingPhone = true;\n\t\t\tuni.showLoading({ title: '绑定中...' });\n\n\t\t\ttry {\n\t\t\t\t// 获取unionid\n\t\t\t\tconst unionid = uni.getStorageSync('unionid');\n\t\t\t\tif (!unionid) {\n\t\t\t\t\tthrow new Error('缺少微信用户标识，请重新登录');\n\t\t\t\t}\n\t\t\t\tconst registerID = uni.getStorageSync(\"registerID\")\n\n\t\t\t\t// 调用绑定接口\n\t\t\t\tconst params = {\n\t\t\t\t\tphone: phone,\n\t\t\t\t\tshortCode: code,\n\t\t\t\t\tunionid: unionid,\n\t\t\t\t\tplatform: 1, // 师傅端\n\t\t\t\t\tregistrationId: registerID || 'xxx' // 极光推送id\n\t\t\t\t};\n\n\t\t\t\tconsole.log('绑定手机号参数:', params);\n\n\t\t\t\tconst response = await this.$api.user.register(params);\n\t\t\t\tconsole.log('绑定手机号响应:', response);\n\n\t\t\t\tif (response.code === '200') {\n\t\t\t\t\tthis.showToast('绑定成功', 'success');\n\n\t\t\t\t\t// 更新用户信息\n\t\t\t\t\tconst updatedUserInfo = {\n\t\t\t\t\t\t...this.userInfo,\n\t\t\t\t\t\tphone: phone\n\t\t\t\t\t};\n\n\t\t\t\t\tthis.updateUserItem({\n\t\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\t\tval: updatedUserInfo\n\t\t\t\t\t});\n\n\t\t\t\t\t// 更新本地存储\n\t\t\t\t\tuni.setStorageSync('phone', phone);\n\n\t\t\t\t\t// 更新师傅信息存储\n\t\t\t\t\tconst shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};\n\t\t\t\t\tshiInfo.mobile = phone;\n\t\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify(shiInfo));\n\n\t\t\t\t\t// 关闭弹窗\n\t\t\t\t\tthis.hideBindPhonePopup();\n\n\t\t\t\t\t// 刷新用户信息\n\t\t\t\t\tthis.fetchUserInfo();\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(response.msg || '绑定失败，请重试');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('绑定手机号失败:', error);\n\t\t\t\tthis.showToast(error.message || '绑定失败，请重试');\n\t\t\t} finally {\n\t\t\t\tthis.isBindingPhone = false;\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\n\t\tonGetPhoneNumber(e) {\n\t\t\t// #ifdef APP-PLUS\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/login'\n\t\t\t});\n\t\t\treturn;\n\t\t\t// #endif\n\t\t\tif (e.detail.errMsg !== 'getPhoneNumber:ok') {\n\t\t\t\treturn this.showToast('授权失败，请重试');\n\t\t\t}\n\t\t\tthis.getmylogin();\n\t\t\tthis.isLoading = true;\n\t\t\tuni.showLoading({\n\t\t\t\tmask: true,\n\t\t\t\ttitle: '登录中...'\n\t\t\t});\n\t\t\tconst {\n\t\t\t\tencryptedData,\n\t\t\t\tiv\n\t\t\t} = e.detail;\n\t\t\tuni.checkSession({\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.loginWithWeixin({\n\t\t\t\t\t\tcode: this.code,\n\t\t\t\t\t\tencryptedData,\n\t\t\t\t\t\tiv,\n\t\t\t\t\t\tplatform: 1,\n\t\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.login({\n\t\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\t\tsuccess: res => {\n\t\t\t\t\t\t\tif (res.code) {\n\t\t\t\t\t\t\t\tthis.code = res.code;\n\t\t\t\t\t\t\t\tconsole.log('Refreshed wx.login code:', this.code);\n\t\t\t\t\t\t\t\tthis.loginWithWeixin({\n\t\t\t\t\t\t\t\t\tcode: this.code,\n\t\t\t\t\t\t\t\t\tencryptedData,\n\t\t\t\t\t\t\t\t\tiv,\n\t\t\t\t\t\t\t\t\tplatform: 1,\n\t\t\t\t\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tthis.showToast('获取登录凭证失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tthis.showToast('微信登录失败，请重试');\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\tasync loginWithWeixin(params) {\n\t\t\ttry {\n\t\t\t\tconst response = await this.$api.user.loginuserInfo({\n\t\t\t\t\tcode: params.code,\n\t\t\t\t\tencryptedData: params.encryptedData,\n\t\t\t\t\tiv: params.iv,\n\t\t\t\t\tplatform: 1,\n\t\t\t\t\tpid: this.inviteCode\n\t\t\t\t});\n\t\t\t\tconsole.log(response)\n\t\t\t\tif (!response || !response.data.token) {\n\t\t\t\t\tthrow new Error('请重新登录');\n\t\t\t\t}\n\t\t\t\tuni.setStorageSync('token', response.data.token);\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'autograph',\n\t\t\t\t\tval: response.data.token\n\t\t\t\t});\n\t\t\t\tconst userInfoRes = await this.$api.user.userInfo();\n\t\t\t\tif (!userInfoRes || typeof userInfoRes !== 'object') {\n\t\t\t\t\tthrow new Error('获取用户信息失败');\n\t\t\t\t}\n\t\t\t\tconsole.log(userInfoRes)\n\t\t\t\tconst initialUserInfo = {\n\t\t\t\t\tphone: userInfoRes.data.phone || '',\n\t\t\t\t\tavatarUrl: userInfoRes.data.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: userInfoRes.data.nickName || '微信用户',\n\t\t\t\t\tuserId: userInfoRes.data.id || '',\n\t\t\t\t\tpid: userInfoRes.data.pid || ''\n\t\t\t\t};\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: initialUserInfo\n\t\t\t\t});\n\t\t\t\tthis.saveUserInfoToStorage(initialUserInfo);\n\t\t\t\tconst shifuStatusRes = await this.$api.shifu.getshifstutas({\n\t\t\t\t\tuserId: initialUserInfo.userId\n\t\t\t\t});\n\t\t\t\tconsole.log('师傅状态响应:', shifuStatusRes);\n\t\t\t\tthis.shifustatus = Number(shifuStatusRes.data) !== undefined ? Number(shifuStatusRes.data) : -1;\n\t\t\t\tconsole.log('设置师傅状态:', this.shifustatus);\n\n\t\t\t\tif (shifuStatusRes.data === -1) {\n\t\t\t\t\tconst userinster = {\n\t\t\t\t\t\tuserId: initialUserInfo.userId,\n\t\t\t\t\t\tmobile: initialUserInfo.phone,\n\t\t\t\t\t\taddress: this.regeocode?.regeocode?.formatted_address || '',\n\t\t\t\t\t\tcityId: '',\n\t\t\t\t\t\tlabelId: 25,\n\t\t\t\t\t\tlng: this.regeocode?.lng || uni.getStorageSync('lng') || 0,\n\t\t\t\t\t\tlat: this.regeocode?.lat || uni.getStorageSync('lat') || 0,\n\t\t\t\t\t};\n\t\t\t\t\tconsole.log('Registering master with:', userinster);\n\t\t\t\t\tconst registerRes = await this.$api.shifu.masterEnter(userinster);\n\t\t\t\t\tif (registerRes.code !== \"200\") {\n\t\t\t\t\t\tthrow new Error('Master registration failed');\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log('Master registration successful');\n\t\t\t\t\t// 注册成功后重新获取状态\n\t\t\t\t\tconst newStatusRes = await this.$api.shifu.getshifstutas({\n\t\t\t\t\t\tuserId: initialUserInfo.userId\n\t\t\t\t\t});\n\t\t\t\t\tthis.shifustatus = Number(newStatusRes.data) !== undefined ? Number(newStatusRes.data) : -1;\n\t\t\t\t\tconsole.log('注册后的师傅状态:', this.shifustatus);\n\t\t\t\t}\n\n\t\t\t\tconst masterRess = await this.$api.shifu.getMaster();\n\t\t\t\tconsole.log('getMaster响应:', masterRess);\n\t\t\t\tif (!masterRess || typeof masterRess !== 'object') {\n\t\t\t\t\tthrow new Error('获取师傅信息失败');\n\t\t\t\t}\n\t\t\t\tlet masterRes = masterRess.data;\n\n\t\t\t\t// 如果getMaster返回了状态信息，优先使用\n\t\t\t\tif (masterRes && masterRes.status !== undefined) {\n\t\t\t\t\tthis.shifustatus = Number(masterRes.status);\n\t\t\t\t\tconsole.log('从getMaster获取的状态:', this.shifustatus);\n\t\t\t\t} else {\n\t\t\t\t\t// 如果getMaster没有返回状态，使用之前获取的状态\n\t\t\t\t\tconsole.log('getMaster未返回状态，使用当前状态:', this.shifustatus);\n\t\t\t\t}\n\n\t\t\t\tconst userInfo = {\n\t\t\t\t\tphone: masterRes.mobile || initialUserInfo.phone || '',\n\t\t\t\t\tavatarUrl: masterRes.avatarUrl || initialUserInfo.avatarUrl || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: masterRes.coachName || initialUserInfo.nickName || '微信用户',\n\t\t\t\t\tuserId: masterRes.userId || initialUserInfo.userId || '',\n\t\t\t\t\tshifuId: masterRes.id || '',\n\t\t\t\t\tpid: masterRes.pid || initialUserInfo.pid || ''\n\t\t\t\t};\n\t\t\t\t// 保存师傅信息到本地存储\n\t\t\t\tconst shiInfoData = {\n\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\tshifuId: userInfo.shifuId,\n\t\t\t\t\tuserId: userInfo.userId,\n\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\tmessagePush: Number(masterRes.messagePush) || -1\n\t\t\t\t};\n\n\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify(shiInfoData));\n\t\t\t\t// 立即更新缓存时间戳，确保状态能立即显示\n\t\t\t\tuni.setStorageSync('shiInfoTimestamp', Date.now());\n\n\t\t\t\tconsole.log('登录完成，保存师傅信息:', shiInfoData);\n\t\t\t\tconsole.log('最终师傅状态:', this.shifustatus);\n\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: userInfo\n\t\t\t\t});\n\t\t\t\tthis.saveUserInfoToStorage(userInfo);\n\n\t\t\t\tconst modalShownKey = `certificationModalShown_${userInfo.userId}_${this.shifustatus}`;\n\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\t\t\t\tif (!hasShownModal && (this.shifustatus === -1 || this.shifustatus === 4) && this.isLoggedIn) {\n\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t}\n\n\t\t\t\tthis.showToast('登录成功', 'success');\n\t\t\t\tthis.hideLoginPopup();\n\t\t\t\tthis.debounceGetHighlight();\n\n\t\t\t\t// 强制更新界面，确保状态文本立即显示\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t// 确保状态文本能立即更新\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t// 再次确认状态已正确设置\n\t\t\t\t\tconsole.log('登录完成后的最终状态检查:', {\n\t\t\t\t\t\tshifustatus: this.shifustatus,\n\t\t\t\t\t\tstatusText: this.statusText,\n\t\t\t\t\t\tlabelName: this.labelName,\n\t\t\t\t\t\tcachedShiInfo: uni.getStorageSync('shiInfo')\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Login error:', error);\n\t\t\t\tthis.showToast(error.message || '登录失败，请稍后重试');\n\t\t\t\tthis.handleInvalidSession();\n\t\t\t} finally {\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\n\n\n\t\tshowToast(title, icon = 'none') {\n\t\t\tuni.showToast({\n\t\t\t\ttitle,\n\t\t\t\ticon,\n\t\t\t\tduration: 2000\n\t\t\t});\n\t\t},\n\t\tasync fetchShifuInfo() {\n\t\t\ttry {\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tconst shiInfoResponses = await this.$api.shifu.getMaster();\n\t\t\t\tlet shiInfoResponse = shiInfoResponses.data\n\t\t\t\tconsole.log('fetchShifuInfo response:', shiInfoResponse);\n\t\t\t\tif (!shiInfoResponses || typeof shiInfoResponses !== 'object') {\n\t\t\t\t\tthrow new Error('获取师傅状态失败: 响应数据无效');\n\t\t\t\t}\n\t\t\t\tthis.shifustatus = Number(shiInfoResponse.status) !== undefined ? Number(shiInfoResponse.status) : -1;\n\t\t\t\tconst userInfo = {\n\t\t\t\t\tphone: shiInfoResponse.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\tavatarUrl: shiInfoResponse.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: shiInfoResponse.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\tshifuId: shiInfoResponse.id || this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\tuserId: shiInfoResponse.userId || this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\tpid: shiInfoResponse.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t};\n\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\tmobile: userInfo.phone,\n\t\t\t\t\tavatarUrl: userInfo.avatarUrl,\n\t\t\t\t\tcoachName: userInfo.nickName,\n\t\t\t\t\tuserId: userInfo.userId,\n\t\t\t\t\tshifuId: userInfo.shifuId,\n\t\t\t\t\tpid: userInfo.pid,\n\t\t\t\t\tstatus: this.shifustatus,\n\t\t\t\t\tmessagePush: Number(shiInfoResponse.messagePush) || -1\n\t\t\t\t}));\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: userInfo\n\t\t\t\t});\n\t\t\t\tconst modalShownKey = `certificationModalShown_${userInfo.userId}_${this.shifustatus}`;\n\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\t\t\t\tif (!hasShownModal && (this.shifustatus === -1 || this.shifustatus === 4) && this.isLoggedIn) {\n\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t}\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('fetchShifuInfo error:', error);\n\t\t\t\tthis.shifustatus = -1;\n\t\t\t\tconst defaultUserInfo = {\n\t\t\t\t\tphone: this.userInfo.phone || uni.getStorageSync('phone') || '',\n\t\t\t\t\tavatarUrl: this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',\n\t\t\t\t\tnickName: this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',\n\t\t\t\t\tuserId: this.userInfo.userId || uni.getStorageSync('userId') || '',\n\t\t\t\t\tpid: this.userInfo.pid || uni.getStorageSync('pid') || ''\n\t\t\t\t};\n\t\t\t\tuni.setStorageSync('shiInfo', JSON.stringify({\n\t\t\t\t\tmobile: defaultUserInfo.phone,\n\t\t\t\t\tavatarUrl: defaultUserInfo.avatarUrl,\n\t\t\t\t\tcoachName: defaultUserInfo.nickName,\n\t\t\t\t\tuserId: defaultUserInfo.userId,\n\t\t\t\t\tshifuId: '', // 默认为空字符串\n\t\t\t\t\tpid: defaultUserInfo.pid,\n\t\t\t\t\tstatus: -1,\n\t\t\t\t\tmessagePush: -1\n\t\t\t\t}));\n\t\t\t\tthis.updateUserItem({\n\t\t\t\t\tkey: 'userInfo',\n\t\t\t\t\tval: defaultUserInfo\n\t\t\t\t});\n\t\t\t\tconst modalShownKey = `certificationModalShown_${defaultUserInfo.userId}_${defaultUserInfo.status}`;\n\t\t\t\tconst hasShownModal = uni.getStorageSync(modalShownKey);\n\t\t\t\tif (!hasShownModal && defaultUserInfo.status === -1 && this.isLoggedIn) {\n\t\t\t\t\tthis.showCertificationPopup();\n\t\t\t\t\tuni.setStorageSync(modalShownKey, 'true');\n\t\t\t\t}\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.isLoading = false;\n\t\t\t}\n\t\t},\n\t\tgetshifustatus() {\n\t\t\tconst shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};\n\t\t\tthis.shifustatus = shiInfo.status;\n\t\t\tconsole.log('getshifustatus:', this.shifustatus);\n\t\t},\n\t\tshowCertificationPopup() {\n\t\t\tconsole.log('showCertificationPopup called, current shifustatus:', this.shifustatus);\n\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: this.shifustatus === -1 ? '您尚未成为师傅，是否前往认证？' : '您的师傅认证被驳回，是否重新认证？',\n\t\t\t\t\tconfirmText: '去认证',\n\t\t\t\t\tcancelText: '再想想',\n\t\t\t\t\tcancelable: true,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tconst targetUrl = '/shifu/Settle';\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: targetUrl,\n\t\t\t\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t\t\t\tconsole.error('Navigation to certification failed:', err);\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '跳转认证页面失败',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('Modal failed:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\thandleNavigate(url) {\n\t\t\tconst directNavigatePaths = ['/shifu/Settle', '/user/promotion', '/shifu/master_Info'];\n\n\t\t\tif (directNavigatePaths.includes(url)) {\n\t\t\t\tthis.navigateTo(url);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 先检查是否已登录\n\t\t\tif (!this.isLoggedIn) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.showLoginPopup();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '你还不是师傅',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.showCertificationPopup();\n\t\t\t} else if (this.shifustatus === 1) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '师傅状态在审核中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} else if (this.shifustatus === 2) {\n\t\t\t\tthis.navigateTo(url);\n\t\t\t} else {\n\t\t\t\tthis.navigateTo(url); // Fallback to navigate if status is unexpected.\n\t\t\t}\n\t\t},\n\t\thandleCallKf() {\n\t\t\t// 先检查是否已登录\n\t\t\tif (!this.isLoggedIn) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.showLoginPopup();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (this.shifustatus === -1 || this.shifustatus === 4) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '你还不是师傅',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tthis.showCertificationPopup();\n\t\t\t} else if (this.shifustatus === 1) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '师傅状态在审核中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} else if (this.shifustatus === 2) {\n\t\t\t\tthis.callkf();\n\t\t\t}\n\t\t},\n\t\tcallkf() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '联系客服功能待实现',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\n\t\tshowToast(title, icon = 'none') {\n\t\t\tuni.showToast({\n\t\t\t\ttitle,\n\t\t\t\ticon,\n\t\t\t\tduration: 2000\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n/* Login Popup Styles */\n.login-popup-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tz-index: 2000;\n\tdisplay: flex;\n\talign-items: flex-end;\n\tjustify-content: center;\n}\n\n.login-popup {\n\tbackground-color: #fff;\n\twidth: 100%;\n\tborder-radius: 40rpx 40rpx 0 0;\n\tposition: relative;\n\tmax-height: 60vh;\n\tpadding-bottom: 10rpx;\n\tanimation: slideUp 0.3s ease-out;\n}\n\n@keyframes slideUp {\n\tfrom {\n\t\ttransform: translateY(100%);\n\t}\n\n\tto {\n\t\ttransform: translateY(0);\n\t}\n}\n\n.close-btn {\n\tposition: absolute;\n\ttop: 30rpx;\n\tright: 30rpx;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tcolor: #999;\n\tfont-size: 40rpx;\n\tz-index: 10;\n}\n\n.popup-content {\n\tpadding: 80rpx 60rpx 40rpx;\n\ttext-align: center;\n}\n\n.welcome-title {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.welcome-subtitle {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tmargin-bottom: 80rpx;\n}\n\n.agreement-section {\n\tmargin-bottom: 60rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.checkbox-container {\n\tdisplay: flex;\n\talign-items: flex-start;\n\ttext-align: left;\n\tmax-width: 560rpx;\n}\n\n.checkbox {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tborder: 2rpx solid #ddd;\n\tborder-radius: 6rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n\tmargin-top: 4rpx;\n\tflex-shrink: 0;\n\tbackground-color: #fff;\n\ttransition: all 0.2s;\n\n\t&.checked {\n\t\tbackground-color: #00C853;\n\t\tborder-color: #00C853;\n\t\tcolor: #fff;\n\t}\n\n\t.iconfont {\n\t\tfont-size: 24rpx;\n\t}\n}\n\n.agreement-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\n\t.link {\n\t\tcolor: #00C853;\n\t}\n}\n\n.phone-login-btn {\n\twidth: 100%;\n\theight: 100rpx;\n\tbackground: linear-gradient(135deg, #00C853, #4CAF50);\n\tborder: none;\n\tborder-radius: 50rpx;\n\tcolor: #fff;\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 8rpx 20rpx rgba(0, 200, 83, 0.3);\n\ttransition: all 0.2s;\n\n\t&:active:not(.disabled) {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);\n\t}\n\n\t&.disabled {\n\t\tbackground: #ccc;\n\t\tbox-shadow: none;\n\t\topacity: 0.6;\n\t}\n\n\t&::after {\n\t\tborder: none;\n\t}\n}\n\n.bind-phone-container {\n\tmargin-top: 10rpx;\n}\n\n.bind-phone-btn {\n\tbackground: none;\n\tborder: 2rpx solid rgba(255, 255, 255, 0.8);\n\tborder-radius: 32rpx;\n\tcolor: #fff;\n\tfont-size: 28rpx;\n\tline-height: 1.5;\n\tpadding: 8rpx 24rpx;\n\ttransition: all 0.2s;\n\n\t&:active:not([disabled]) {\n\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\ttransform: scale(0.98);\n\t}\n\n\t&[disabled] {\n\t\topacity: 0.6;\n\t}\n\n\t&::after {\n\t\tborder: none;\n\t}\n}\n\n.input-group {\n\tmargin-bottom: 40rpx;\n\n\t.input-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: #f8fafc;\n\t\tborder: 2rpx solid #e2e8f0;\n\t\tborder-radius: 16rpx;\n\t\tmargin-bottom: 24rpx;\n\t\tmin-height: 88rpx;\n\t\tpadding: 0;\n\t\ttransition: all 0.3s ease;\n\n\t\t&:focus-within {\n\t\t\tborder-color: #3b82f6;\n\t\t\tbox-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.08);\n\t\t}\n\n\t\t.input-icon {\n\t\t\twidth: 50rpx;\n\t\t\theight: 50rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tmargin-left: 24rpx;\n\t\t\tbackground: rgba(59, 130, 246, 0.08);\n\t\t\tborder-radius: 50%;\n\t\t\tflex-shrink: 0;\n\t\t}\n\n\t\t.input-field {\n\t\t\tflex: 1;\n\t\t\tmargin-left: 24rpx;\n\t\t\tmargin-right: 16rpx;\n\t\t\tfont-size: 32rpx;\n\t\t\tcolor: #1e293b;\n\t\t\tbackground: transparent;\n\t\t\tborder: none;\n\t\t\toutline: none;\n\n\t\t\t&::placeholder {\n\t\t\t\tcolor: #94a3b8;\n\t\t\t}\n\t\t}\n\n\t\t.sms-btn {\n\t\t\tbackground: linear-gradient(135deg, #3b82f6, #1d4ed8);\n\t\t\tcolor: #fff;\n\t\t\tpadding: 16rpx 24rpx;\n\t\t\tborder-radius: 12rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 500;\n\t\t\tmargin-right: 16rpx;\n\t\t\ttransition: all 0.3s ease;\n\t\t\tbox-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);\n\t\t\tflex-shrink: 0;\n\n\t\t\t&.disabled {\n\t\t\t\tbackground: #cbd5e1;\n\t\t\t\tcolor: #64748b;\n\t\t\t\tbox-shadow: none;\n\t\t\t}\n\n\t\t\t&:not(.disabled):active {\n\t\t\t\ttransform: scale(0.95);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.alternative-login {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 40rpx;\n\n\t.divider-line {\n\t\tflex: 1;\n\t\theight: 1rpx;\n\t\tbackground-color: #eee;\n\t}\n\n\t.divider-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #999;\n\t\tmargin: 0 30rpx;\n\t}\n}\n\n.sms-login-btn {\n\twidth: 100%;\n\theight: 88rpx;\n\tbackground-color: #fff;\n\tborder: 2rpx solid #ddd;\n\tborder-radius: 44rpx;\n\tcolor: #666;\n\tfont-size: 32rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\ttransition: all 0.2s;\n\n\t&:active {\n\t\tbackground-color: #f8f8f8;\n\t\tborder-color: #00C853;\n\t}\n\n\t&::after {\n\t\tborder: none;\n\t}\n\n\t.iconfont {\n\t\tmargin-right: 16rpx;\n\t\tfont-size: 36rpx;\n\t}\n}\n\n/* Floating Contact Button Styles */\n.floating-contact {\n\tposition: fixed;\n\tbottom: 150rpx;\n\tright: 30rpx;\n\tz-index: 1000;\n\tbackground-color: #fff;\n\tborder-radius: 50rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\n\tpadding: 10rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.contact-container {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.contact-btn {\n\tbackground: none;\n\tborder: none;\n\tcolor: #576b95;\n\tfont-size: 30rpx;\n\tline-height: 1.5;\n\tpadding: 10rpx 20rpx;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.contact-btn:active {\n\tbackground-color: #ededee;\n}\n\n/* Existing Styles */\n.pages-mine {\n\tbackground-color: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n\n\t.header {\n\t\theight: 292rpx;\n\t\tbackground-color: #599EFF;\n\t\tposition: relative;\n\n\t\t.header-content {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 40rpx 30rpx 0;\n\n\t\t\t.avatar_view {\n\t\t\t\twidth: 120rpx;\n\t\t\t\theight: 120rpx;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\toverflow: hidden;\n\t\t\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n\n\t\t\t\t.avatar {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.user-info {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-left: 20rpx;\n\t\t\t\tcolor: #fff;\n\n\t\t\t\t.user-info-logged {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tgap: 10rpx;\n\t\t\t\t}\n\n\t\t\t\t.nickname {\n\t\t\t\t\tfont-size: 36rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\n\t\t\t\t.phone-number {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\topacity: 0.9;\n\t\t\t\t}\n\n\t\t\t\tbutton {\n\t\t\t\t\tbackground: none;\n\t\t\t\t\tborder: 2rpx solid rgba(255, 255, 255, 0.5);\n\t\t\t\t\tborder-radius: 32rpx;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tline-height: 1.5;\n\t\t\t\t\tpadding: 10rpx 30rpx;\n\n\t\t\t\t\t&.loading {\n\t\t\t\t\t\topacity: 0.7;\n\t\t\t\t\t}\n\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tborder: none;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.status-badge {\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t\tpadding: 8rpx 20rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tline-height: 1.2;\n\t\t\t\t\tborder-radius: 20rpx;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\twidth: fit-content;\n\t\t\t\t\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n\t\t\t\t}\n\n\t\t\t\t.status-not-registered {\n\t\t\t\t\tbackground-color: #b0b0b0;\n\t\t\t\t}\n\n\t\t\t\t.status-pending {\n\t\t\t\t\tbackground-color: #f4b400;\n\t\t\t\t}\n\n\t\t\t\t.status-approved {\n\t\t\t\t\tbackground-color: #f5a623;\n\t\t\t\t}\n\n\t\t\t\t.status-rejected {\n\t\t\t\t\tbackground-color: #f44336;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.settings {\n\t\t\t\tpadding: 10rpx;\n\n\t\t\t\t.icon-xitong {\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.box1 {\n\t\tmargin-top: -20rpx;\n\t\tborder-radius: 36rpx 36rpx 0 0;\n\t\tposition: relative;\n\t\tz-index: 10;\n\t}\n\n\t.mine-menu-list {\n\t\tbackground-color: #fff;\n\t\tmargin: 0 20rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\tmargin-bottom: 20rpx;\n\n\t\t.menu-title {\n\t\t\theight: 90rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tpadding: 0 30rpx 0 40rpx;\n\t\t\tborder-bottom: 1px solid #f0f0f0;\n\n\t\t\t.f-paragraph {\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t}\n\n\t\t.flex-warp {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tpadding: 30rpx 0;\n\n\t\t\t.order-item {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\twidth: 25%;\n\t\t\t\tfont-size: 25rpx;\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\ttransition: transform 0.2s;\n\n\t\t\t\t&:active {\n\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t}\n\n\t\t\t\t.icon-container {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t}\n\n\t\t\t\t.number-circle {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: -10rpx;\n\t\t\t\t\tright: -5rpx;\n\t\t\t\t\twidth: 30rpx;\n\t\t\t\t\theight: 30rpx;\n\t\t\t\t\tbackground-color: #ff4d4f;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.mt-sm {\n\t\t\t\tmargin-top: 16rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.spacer {\n\t\theight: 20rpx;\n\t\tbackground-color: transparent;\n\t}\n\n\t.mine-tool-grid {\n\t\tbackground-color: #fff;\n\t\tmargin: 0 20rpx 30rpx;\n\t\tborder-radius: 12rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n\t\tpadding: 30rpx;\n\n\t\t.grid-container {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\tgap: 20rpx;\n\t\t}\n\n\t\t.grid-item {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\twidth: calc(33.33% - 20rpx);\n\t\t\tmin-width: 140rpx;\n\t\t\ttransition: transform 0.2s ease;\n\n\t\t\t&:active {\n\t\t\t\ttransform: scale(0.95);\n\t\t\t}\n\n\t\t\t.grid-icon-container {\n\t\t\t\twidth: 80rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tborder-radius: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tposition: relative;\n\n\t\t\t\t&.switch-identity {\n\t\t\t\t\t/* Specific styling for switch-identity icon */\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.grid-text {\n\t\t\t\tfont-size: 25rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-weight: 500;\n\t\t\t\ttext-align: center;\n\t\t\t\tline-height: 1.2;\n\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t}\n\n\t\t\t.contact-btn-wrapper {\n\t\t\t\tbackground: none;\n\t\t\t\tborder: none;\n\t\t\t\tpadding: 0;\n\t\t\t\tmargin: 0;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tline-height: 1;\n\n\t\t\t\t&::after {\n\t\t\t\t\tborder: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.flex-between {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t}\n}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mine.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755133984292\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}