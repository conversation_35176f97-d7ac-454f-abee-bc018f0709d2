{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?19d3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?84fc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?5629", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?06ac", "uni-app:///shifu/Margin.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?bcd7", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/shifu/Margin.vue?4fb3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "money", "cashPledge", "cashPledgeFreeze", "refundableAmount", "reason", "config<PERSON>ash<PERSON>ledge", "methods", "getCurrentPlatform", "handleAppWechatPay", "console", "uni", "orderInfo", "appid", "noncestr", "package", "partnerid", "prepayid", "timestamp", "sign", "title", "icon", "handleMiniProgramPay", "timeStamp", "nonceStr", "signType", "paySign", "appId", "success", "fail", "tui<PERSON><PERSON>", "getMoney", "res", "getprice", "submit", "confirmPay", "payPrice", "couponId", "type", "onLoad", "refreshData", "Promise", "onPullDownRefresh", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+Bz2B;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MAKA;MAKA;IACA;IACA;IACAC;MAAA;QAAA;MACAC;MACAA;MACAC;QACA;QACAC;MAAA,mEACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,kEAEA;QACAT;QACAC;UACAS;UACAC;QACA;QACA;QACA;MACA,+DACA;QACAX;QACA;UACAC;YACAS;YACAC;UACA;QACA;UACAV;YACAS;YACAC;UACA;QACA;MACA,yBACA;IACA;IAEA;IACAC;MAAA;MACA;QACAC;QAAA;QACAC;QACAT;QACAU;QACAC;MACA;MACAhB;MACAC;QACA;QACAY;QACAC;QACAT;QACAC;QACAS;QACAC;QACAC;QACAC;UACA;UACAlB;UACAC;YACAS;YACAC;UACA;UACA;UACA;QACA;QACAQ;UACA;UACAnB;UACAA;UACA;YACAC;cACAS;cACAC;YACA;UACA;YACAV;cACAS;cACAC;YACA;UACA;UACAX;UACAC;YACAS;YACAC;UACA;QACA;MACA;IACA;IACAS;MAAA;MACA;QACA;UACAnB;YACAU;YACAD;UACA;QACA;UACAT;YACAU;YACAD;UACA;UACA;UACA;QACA;MACA;IACA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAtB;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAD;gBACA;kBACAtB;gBACA;kBACAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAwB;MACA;QACA;MACA;QACAvB;UACAU;UACAD;QACA;MACA;IACA;IACAe;MAAA;MACA;MACAzB;MACA;QACA0B;QACAC;QACAC;MACA;QACA;UACA3B;YACAS;YACAC;UACA;QACA;UACAX;UACA;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UAMA;YACAa;YAAA;YACAC;YACAT;YACAU;YACAC;UACA;UACAhB;UACAA;UACA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;YACA;YACAA;YACA;UACA;QAEA;MACA;IACA;EACA;EACA6B;IACA;IACA;EACA;EAEA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAGAC,aACA,mBACA,kBACA;YAAA;cACA/B;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAgC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cACA/B;gBACAS;gBACAC;gBACAsB;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAjC;cACAC;gBACAS;gBACAC;cACA;YAAA;cAAA;cAEAV;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC9TA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/Margin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/Margin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Margin.vue?vue&type=template&id=92a41b30&scoped=true&\"\nvar renderjs\nimport script from \"./Margin.vue?vue&type=script&lang=js&\"\nexport * from \"./Margin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92a41b30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/Margin.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=template&id=92a41b30&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"box\">\r\n\t\t\t<view class=\"money\">{{ refundableAmount }}</view>\r\n\t\t\t<view class=\"title\">保证金金额（元）</view>\r\n\r\n\t\t\t<!-- 冻结信息显示 -->\r\n\t\t\t<view v-if=\"cashPledgeFreeze > 0\" class=\"freeze-info\">\r\n\t\t\t\t<view class=\"freeze-title\">保证金扣除信息</view>\r\n\t\t\t\t<view class=\"freeze-item\">\r\n\t\t\t\t\t<text class=\"freeze-label\">已交金额：</text>\r\n\t\t\t\t\t<text class=\"freeze-value\">{{ cashPledge }}元</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"freeze-item\">\r\n\t\t\t\t\t<text class=\"freeze-label\">扣除金额：</text>\r\n\t\t\t\t\t<text class=\"freeze-value freeze-amount\">{{ cashPledgeFreeze }}元</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"reason\" class=\"freeze-item\">\r\n\t\t\t\t\t<text class=\"freeze-label\">扣除原因：</text>\r\n\t\t\t\t\t<text class=\"freeze-value\">{{ reason }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- <view class=\"btn\" @click=\"submit\">缴纳保证金</view> -->\r\n\t\t\t<view class=\"btn\" @click=\"submit\">缴纳保证金</view>\r\n\t\t\t<view v-if=\"money > 0\" class=\"btn\" @click=\"tuikuan\">退还保证金</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tmoney: '',\r\n\t\t\tcashPledge: '', // 交的钱\r\n\t\t\tcashPledgeFreeze: '', // 冻结的\r\n\t\t\trefundableAmount: '', // 可退金额\r\n\t\t\treason: '', // 冻结的原因\r\n\t\t\tconfigCashPledge: '', // 配置中的保证金金额\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetCurrentPlatform() {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\treturn 'app-plus';\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\treturn 'mp-weixin';\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\treturn 'h5';\r\n\t\t\t// #endif\r\n\t\t\treturn 'unknown';\r\n\t\t},\r\n\t\t// APP微信支付处理\r\n\t\thandleAppWechatPay(obj) {\r\n\t\t\tconsole.log(111)\r\n\t\t\tconsole.log(obj)\r\n\t\t\tuni.requestPayment({\r\n\t\t\t\t\"provider\": \"wxpay\",\r\n\t\t\t\torderInfo: 'orderInfo',\r\n\t\t\t\torderInfo: {\r\n\t\t\t\t\tappid: obj.appId,\r\n\t\t\t\t\tnoncestr: obj.nonceStr,\r\n\t\t\t\t\tpackage: 'Sign=WXPay',\r\n\t\t\t\t\tpartnerid: obj.partnerId,\r\n\t\t\t\t\tprepayid: obj.prepayId,\r\n\t\t\t\t\ttimestamp: String(obj.timestamp),\r\n\t\t\t\t\tsign: obj.sign\r\n\t\t\t\t},\r\n\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconsole.log('APP微信支付成功', res);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 支付成功后刷新数据\r\n\t\t\t\t\tthis.refreshData();\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('APP微信支付失败:', err);\r\n\t\t\t\t\tif (err.errMsg && err.errMsg.includes('cancel')) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '您已取消支付',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 微信小程序支付处理（保持原有逻辑）\r\n\t\thandleMiniProgramPay(obj) {\r\n\t\t\tconst paymentParams = {\r\n\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string\r\n\t\t\t\tnonceStr: obj.nonceStr,\r\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\r\n\t\t\t\tsignType: 'MD5',\r\n\t\t\t\tpaySign: obj.sign\r\n\t\t\t};\r\n\t\t\tconsole.log(JSON.stringify(paymentParams));\r\n\t\t\tuni.requestPayment({\r\n\t\t\t\t\"provider\": 'wxpay',\r\n\t\t\t\ttimeStamp: String(obj.timestamp),\r\n\t\t\t\tnonceStr: obj.nonceStr,\r\n\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\r\n\t\t\t\tpartnerid: obj.partnerId,\r\n\t\t\t\tsignType: \"MD5\",\r\n\t\t\t\tpaySign: obj.sign,\r\n\t\t\t\tappId: obj.appId,\r\n\t\t\t\tsuccess: (res1) => {\r\n\t\t\t\t\t// 支付成功回调\r\n\t\t\t\t\tconsole.log('支付成功', res1);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 支付成功后刷新数据\r\n\t\t\t\t\tthis.refreshData();\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t// 支付失败回调\r\n\t\t\t\t\tconsole.error('requestPayment fail object:', err);\r\n\t\t\t\t\tconsole.error('requestPayment fail JSON:', JSON.stringify(err));\r\n\t\t\t\t\tif (err.errMsg.includes('fail cancel')) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '您已取消支付',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付失败，请稍后重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.error('支付失败', err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '支付失败请检查网络',\r\n\t\t\t\t\t\ticon: 'error'\r\n\t\t\t\t\t})\r\n\t\t\t\t},\r\n\t\t\t})\r\n\t\t},\r\n\t\ttuikuan() {\r\n\t\t\tthis.$api.shifu.tuikuanBzj().then(res => {\r\n\t\t\t\tif (res.code === '-1') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\ttitle: res.data\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 退款成功后刷新数据\r\n\t\t\t\t\tthis.refreshData()\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tasync getMoney() {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await this.$api.shifu.seeBzj();\r\n\t\t\t\tif (res === -1) {\r\n\t\t\t\t\tthis.money = 0;\r\n\t\t\t\t\tthis.cashPledge = 0;\r\n\t\t\t\t\tthis.cashPledgeFreeze = 0;\r\n\t\t\t\t\tthis.refundableAmount = 0;\r\n\t\t\t\t\tthis.reason = '';\r\n\t\t\t\t} else if (res.code === '200' && res.data) {\r\n\t\t\t\t\t// 根据新的数据结构设置各个字段\r\n\t\t\t\t\tthis.cashPledge = res.data.cashPledge || 0; // 交的钱\r\n\t\t\t\t\tthis.cashPledgeFreeze = res.data.cashPledgeFreeze || 0; // 冻结的\r\n\t\t\t\t\tthis.refundableAmount = res.data.refundableAmount || 0; // 可退金额\r\n\t\t\t\t\tthis.reason = res.data.reason || ''; // 冻结的原因\r\n\r\n\t\t\t\t\t// money 显示已交的保证金金额\r\n\t\t\t\t\tthis.money = this.cashPledge;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.money = 0;\r\n\t\t\t\t\tthis.cashPledge = 0;\r\n\t\t\t\t\tthis.cashPledgeFreeze = 0;\r\n\t\t\t\t\tthis.refundableAmount = 0;\r\n\t\t\t\t\tthis.reason = '';\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取保证金失败:', error);\r\n\t\t\t\tthis.money = 0;\r\n\t\t\t\tthis.cashPledge = 0;\r\n\t\t\t\tthis.cashPledgeFreeze = 0;\r\n\t\t\t\tthis.refundableAmount = 0;\r\n\t\t\t\tthis.reason = '';\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync getprice() {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await this.$api.base.getConfig();\r\n\t\t\t\tif (res.code === '-1') {\r\n\t\t\t\t\tconsole.error('获取配置失败:', res.msg);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\tthis.configCashPledge = res.data.cashPledge;\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取价格配置失败:', error);\r\n\t\t\t}\r\n\t\t},\r\n\t\tsubmit() {\r\n\t\t\tif (this.money == 0 || this.money == '') {\r\n\t\t\t\tthis.confirmPay()\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\ttitle: '您已缴纳保证金快去接单吧'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tconfirmPay() {\r\n\t\t\tconst platform = this.getCurrentPlatform();\r\n\t\t\tconsole.log('当前平台:', platform);\r\n\t\t\tthis.$api.shifu.nowPay({\r\n\t\t\t\tpayPrice: this.configCashPledge,\r\n\t\t\t\tcouponId: 0,\r\n\t\t\t\ttype: 1,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tif (res.code === '-1') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tlet obj = res.data\r\n\t\t\t\t\tlet packageStr = \"prepay_id=\" + obj.prepayId;\r\n\t\t\t\t\tconsole.log(String(packageStr))\r\n\t\t\t\t\tconsole.log(obj.nonceStr)\r\n\t\t\t\t\tconsole.log(packageStr)\r\n\t\t\t\t\tconsole.log(obj.nonceStr)\r\n\t\t\t\t\tconsole.log(String(obj.timestamp))\r\n\t\t\t\t\tconsole.log(obj.sign)\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t\t\tconst paymentParams = {\r\n\t\t\t\t\t\ttimeStamp: String(obj.timestamp), // 一定要是 string \r\n\t\t\t\t\t\tnonceStr: obj.nonceStr,\r\n\t\t\t\t\t\tpackage: \"prepay_id=\" + obj.prepayId,\r\n\t\t\t\t\t\tsignType: 'MD5',\r\n\t\t\t\t\t\tpaySign: obj.sign\r\n\t\t\t\t\t};\r\n\t\t\t\t\tconsole.log(obj)\r\n\t\t\t\t\tconsole.log(JSON.stringify(paymentParams));\r\n\t\t\t\t\t// 根据平台选择不同的支付方式\r\n\t\t\t\t\tif (platform === 'app-plus') {\r\n\t\t\t\t\t\t// APP环境使用微信支付\r\n\t\t\t\t\t\tconsole.log('APP环境，使用微信支付');\r\n\t\t\t\t\t\tthis.handleAppWechatPay(obj);\r\n\t\t\t\t\t} else if (platform === 'mp-weixin') {\r\n\t\t\t\t\t\t// 微信小程序环境保持原有逻辑\r\n\t\t\t\t\t\tconsole.log('微信小程序环境，使用小程序支付');\r\n\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他环境（H5等）\r\n\t\t\t\t\t\tconsole.log('其他环境，使用默认支付方式');\r\n\t\t\t\t\t\tthis.handleMiniProgramPay(obj);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.getMoney()\r\n\t\tthis.getprice()\r\n\t},\r\n\r\n\t// 刷新数据方法\r\n\tasync refreshData() {\r\n\t\ttry {\r\n\t\t\t// 重新获取保证金信息和价格配置\r\n\t\t\tawait Promise.all([\r\n\t\t\t\tthis.getMoney(),\r\n\t\t\t\tthis.getprice()\r\n\t\t\t]);\r\n\t\t\tconsole.log('数据刷新成功');\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('数据刷新失败:', error);\r\n\t\t}\r\n\t},\r\n\r\n\t// 下拉刷新\r\n\tasync onPullDownRefresh() {\r\n\t\ttry {\r\n\t\t\tawait this.refreshData();\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '刷新成功',\r\n\t\t\t\ticon: 'success',\r\n\t\t\t\tduration: 1000\r\n\t\t\t});\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('下拉刷新失败:', error);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '刷新失败，请重试',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t} finally {\r\n\t\t\tuni.stopPullDownRefresh();\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.page {\r\n\tbackground: #F8F8F8;\r\n\theight: 100vh;\r\n\r\n\t.box {\r\n\t\tpadding: 50rpx 82rpx;\r\n\t\tbackground: #fff;\r\n\r\n\t\t.money {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\twidth: fit-content;\r\n\t\t\tfont-size: 80rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #171717;\r\n\t\t}\r\n\r\n\t\t.title {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\twidth: fit-content;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #171717;\r\n\t\t}\r\n\r\n\t\t.freeze-info {\r\n\t\t\tmargin-top: 40rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #FFF9E6;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tborder-left: 4rpx solid #FF9500;\r\n\r\n\t\t\t.freeze-title {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #FF9500;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.freeze-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.freeze-label {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.freeze-value {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tfont-weight: 500;\r\n\r\n\t\t\t\t\t&.freeze-amount {\r\n\t\t\t\t\t\tcolor: #FF4444;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.btn {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\tmargin-top: 64rpx;\r\n\t\t\twidth: 584rpx;\r\n\t\t\theight: 98rpx;\r\n\t\t\tbackground: #2E80FE;\r\n\t\t\tborder-radius: 12rpx 12rpx 12rpx 12rpx;\r\n\t\t\tline-height: 98rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Margin.vue?vue&type=style&index=0&id=92a41b30&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755133961553\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}