@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-713d0fd3, scroll-view.data-v-713d0fd3, swiper-item.data-v-713d0fd3 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-modal.data-v-713d0fd3 {
  width: 650rpx;
  border-radius: 6px;
  overflow: hidden;
}
.u-modal__title.data-v-713d0fd3 {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  text-align: center;
  padding-top: 25px;
}
.u-modal__content.data-v-713d0fd3 {
  padding: 12px 25px 25px 25px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.u-modal__content__text.data-v-713d0fd3 {
  font-size: 15px;
  color: #606266;
  flex: 1;
}
.u-modal__button-group.data-v-713d0fd3 {
  display: flex;
  flex-direction: row;
}
.u-modal__button-group--confirm-button.data-v-713d0fd3 {
  flex-direction: column;
  padding: 0px 25px 15px 25px;
}
.u-modal__button-group__wrapper.data-v-713d0fd3 {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 48px;
}
.u-modal__button-group__wrapper--confirm.data-v-713d0fd3, .u-modal__button-group__wrapper--only-cancel.data-v-713d0fd3 {
  border-bottom-right-radius: 6px;
}
.u-modal__button-group__wrapper--cancel.data-v-713d0fd3, .u-modal__button-group__wrapper--only-confirm.data-v-713d0fd3 {
  border-bottom-left-radius: 6px;
}
.u-modal__button-group__wrapper--hover.data-v-713d0fd3 {
  background-color: #f3f4f6;
}
.u-modal__button-group__wrapper__text.data-v-713d0fd3 {
  color: #606266;
  font-size: 16px;
  text-align: center;
}

