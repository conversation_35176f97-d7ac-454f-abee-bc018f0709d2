@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-01de4127, scroll-view.data-v-01de4127, swiper-item.data-v-01de4127 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-rate.data-v-01de4127 {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0;
  padding: 0;
  touch-action: none;
}
.u-rate__content.data-v-01de4127 {
  display: flex;
  flex-direction: row;
}
.u-rate__content__item.data-v-01de4127 {
  position: relative;
}
.u-rate__content__item__icon-wrap--half.data-v-01de4127 {
  position: absolute;
  overflow: hidden;
  top: 0;
  left: 0;
}
.u-icon.data-v-01de4127 {
  box-sizing: border-box;
}

