@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-81cd9d32, scroll-view.data-v-81cd9d32, swiper-item.data-v-81cd9d32 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-textarea.data-v-81cd9d32 {
  border-radius: 4px;
  background-color: #fff;
  position: relative;
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 9px;
}
.u-textarea--radius.data-v-81cd9d32 {
  border-radius: 4px;
}
.u-textarea--no-radius.data-v-81cd9d32 {
  border-radius: 0;
}
.u-textarea--disabled.data-v-81cd9d32 {
  background-color: #f5f7fa;
}
.u-textarea__field.data-v-81cd9d32 {
  flex: 1;
  font-size: 15px;
  color: #606266;
  width: 100%;
}
.u-textarea__count.data-v-81cd9d32 {
  position: absolute;
  right: 5px;
  bottom: 2px;
  font-size: 12px;
  color: #909193;
  background-color: #ffffff;
  padding: 1px 4px;
}

