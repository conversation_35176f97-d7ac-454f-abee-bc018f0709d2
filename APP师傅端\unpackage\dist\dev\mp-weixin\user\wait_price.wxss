@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-12e45025 {
  width: 750rpx;
  height: 100vh;
}
.page .map.data-v-12e45025 {
  width: 100%;
  height: 100%;
}
.page .card.data-v-12e45025 {
  width: 686rpx;
  height: 234rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  position: absolute;
  left: 32rpx;
  top: 40rpx;
  padding: 34rpx;
}
.page .card .title.data-v-12e45025 {
  font-size: 40rpx;
  font-weight: 500;
  color: #333333;
}
.page .card .desc.data-v-12e45025 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .card .time.data-v-12e45025 {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .card .time.data-v-12e45025  .u-count-down__text {
  color: #E72427;
}
.page .footer.data-v-12e45025 {
  width: 750rpx;
  height: 192rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 0;
  background-color: #FFFFFF;
}
.page .footer .btn.data-v-12e45025 {
  width: 686rpx;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  text-align: center;
}

