{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-row-notice/u-row-notice.vue?88c6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-row-notice/u-row-notice.vue?0986", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-row-notice/u-row-notice.vue?a520", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-row-notice/u-row-notice.vue?a0e9", "uni-app:///node_modules/uview-ui/components/u-row-notice/u-row-notice.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-row-notice/u-row-notice.vue?a3ca", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-row-notice/u-row-notice.vue?5267"], "names": ["name", "mixins", "data", "animationDuration", "animationPlayState", "nvueInit", "show", "watch", "text", "immediate", "handler", "uni", "fontSize", "speed", "computed", "textStyle", "style", "animationStyle", "innerText", "len", "result", "mounted", "methods", "init", "vue", "boxWidth", "textWidth", "setTimeout", "nvue", "loopAnimation", "getNvueRect", "clickHandler", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsD/2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,eAgBA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QAKA;QAGA;UACAC;QACA;MACA;IACA;IACAC;MAKA;IAEA;IACAC;MAKA;IAEA;EACA;EACAC;IACA;IACAC;MACA;MACAC;MACAA;MACA;IACA;IACAC;MACA;MACAD;MACAA;MACA;IACA;IACA;IACA;IACAE;MACA;QACA;QACAC;MACA;MACA;QACA;QACAC;MACA;MACA;IACA;EACA;EACAC;IAeA;EACA;EACAC;IACAC;MAMA;MAGA;QACAZ;MACA;IACA;IACA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC,cACAC,eACA;gBAAA;gBAAA,OACAf;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAe;gBAAA;gBAAA,OACA;cAAA;gBAAAD;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAoBA;IACAC,4DA6BA;IACAC,uCASA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AAMA;AAAA,2B;;;;;;;;;;;;;ACpRA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-row-notice/u-row-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-row-notice.vue?vue&type=template&id=4661e472&scoped=true&\"\nvar renderjs\nimport script from \"./u-row-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./u-row-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-row-notice.vue?vue&type=style&index=0&id=4661e472&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4661e472\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-row-notice/u-row-notice.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=template&id=4661e472&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.animationStyle])\n  var s1 = _vm.__get_style([_vm.textStyle])\n  var g0 = [\"link\", \"closable\"].includes(_vm.mode)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-notice\"\n\t\t@tap=\"clickHandler\"\n\t>\n\t\t<slot name=\"icon\">\n\t\t\t<view\n\t\t\t\tclass=\"u-notice__left-icon\"\n\t\t\t\tv-if=\"icon\"\n\t\t\t>\n\t\t\t\t<u-icon\n\t\t\t\t\t:name=\"icon\"\n\t\t\t\t\t:color=\"color\"\n\t\t\t\t\tsize=\"19\"\n\t\t\t\t></u-icon>\n\t\t\t</view>\n\t\t</slot>\n\t\t<view\n\t\t\tclass=\"u-notice__content\"\n\t\t\tref=\"u-notice__content\"\n\t\t>\n\t\t\t<view\n\t\t\t\tref=\"u-notice__content__text\"\n\t\t\t\tclass=\"u-notice__content__text\"\n\t\t\t\t:style=\"[animationStyle]\"\n\t\t\t>\n\t\t\t\t<text\n\t\t\t\t\tv-for=\"(item, index) in innerText\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t:style=\"[textStyle]\"\n\t\t\t\t>{{item}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view\n\t\t\tclass=\"u-notice__right-icon\"\n\t\t\tv-if=\"['link', 'closable'].includes(mode)\"\n\t\t>\n\t\t\t<u-icon\n\t\t\t\tv-if=\"mode === 'link'\"\n\t\t\t\tname=\"arrow-right\"\n\t\t\t\t:size=\"17\"\n\t\t\t\t:color=\"color\"\n\t\t\t></u-icon>\n\t\t\t<u-icon\n\t\t\t\tv-if=\"mode === 'closable'\"\n\t\t\t\t@click=\"close\"\n\t\t\t\tname=\"close\"\n\t\t\t\t:size=\"16\"\n\t\t\t\t:color=\"color\"\n\t\t\t></u-icon>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\timport props from './props.js';\n\t// #ifdef APP-NVUE\n\tconst animation = uni.requireNativePlugin('animation')\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * RowNotice 滚动通知中的水平滚动模式\n\t * @description 水平滚动\n\t * @tutorial https://www.uviewui.com/components/noticeBar.html\n\t * @property {String | Number}\ttext\t\t\t显示的内容，字符串\n\t * @property {String}\t\t\ticon\t\t\t是否显示左侧的音量图标 (默认 'volume' )\n\t * @property {String}\t\t\tmode\t\t\t通告模式，link-显示右箭头，closable-显示右侧关闭图标\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色，各图标也会使用文字颜色 (默认 '#f9ae3d' )\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色 (默认 ''#fdf6ec' )\n\t * @property {String | Number}\tfontSize\t\t字体大小，单位px (默认 14 )\n\t * @property {String | Number}\tspeed\t\t\t水平滚动时的滚动速度，即每秒滚动多少px(rpx)，这有利于控制文字无论多少时，都能有一个恒定的速度  (默认 80 )\n\t * \n\t * @event {Function} click 点击通告文字触发\n\t * @event {Function} close 点击右侧关闭图标触发\n\t * @example \n\t */\n\texport default {\n\t\tname: 'u-row-notice',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tanimationDuration: '0', // 动画执行时间\n\t\t\t\tanimationPlayState: 'paused', // 动画的开始和结束执行\n\t\t\t\t// nvue下，内容发生变化，导致滚动宽度也变化，需要标志为是否需要重新计算宽度\n\t\t\t\t// 不能在内容变化时直接重新计算，因为nvue的animation模块上一次的滚动不是刚好结束，会有影响\n\t\t\t\tnvueInit: true,\n\t\t\t\tshow: true\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\ttext: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(newValue, oldValue) {\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tthis.nvueInit = true\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tthis.vue()\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\tif(!uni.$u.test.string(newValue)) {\n\t\t\t\t\t\tuni.$u.error('noticebar组件direction为row时，要求text参数为字符串形式')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfontSize() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvueInit = true\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.vue()\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tspeed() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvueInit = true\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.vue()\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 文字内容的样式\n\t\t\ttextStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle.color = this.color\n\t\t\t\tstyle.fontSize = uni.$u.addUnit(this.fontSize)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tanimationStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle.animationDuration = this.animationDuration\n\t\t\t\tstyle.animationPlayState = this.animationPlayState\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 内部对用户传入的数据进一步分割，放到多个text标签循环，否则如果用户传入的字符串很长（100个字符以上）\n\t\t\t// 放在一个text标签中进行滚动，在低端安卓机上，动画可能会出现抖动现象，需要分割到多个text中可解决此问题\n\t\t\tinnerText() {\n\t\t\t\tlet result = [],\n\t\t\t\t\t// 每组text标签的字符长度\n\t\t\t\t\tlen = 20\n\t\t\t\tconst textArr = this.text.split('')\n\t\t\t\tfor (let i = 0; i < textArr.length; i += len) {\n\t\t\t\t\t// 对拆分的后的text进行slice分割，得到的为数组再进行join拼接为字符串\n\t\t\t\t\tresult.push(textArr.slice(i, i + len).join(''))\n\t\t\t\t}\n\t\t\t\treturn result\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\t// 在APP上(含nvue)，监听当前webview是否处于隐藏状态(进入下一页时即为hide状态)\n\t\t\t// 如果webivew隐藏了，为了节省性能的损耗，应停止动画的执行，同时也是为了保持进入下一页返回后，滚动位置保持不变\n\t\t\tvar pages = getCurrentPages()\n\t\t\tvar page = pages[pages.length - 1]\n\t\t\tvar currentWebview = page.$getAppWebview()\n\t\t\tcurrentWebview.addEventListener('hide', () => {\n\t\t\t\tthis.webviewHide = true\n\t\t\t})\n\t\t\tcurrentWebview.addEventListener('show', () => {\n\t\t\t\tthis.webviewHide = false\n\t\t\t})\n\t\t\t// #endif\n\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvue()\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.vue()\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\tif(!uni.$u.test.string(this.text)) {\n\t\t\t\t\tuni.$u.error('noticebar组件direction为row时，要求text参数为字符串形式')\n\t\t\t\t}\n\t\t\t},\n\t\t\t// vue版处理\n\t\t\tasync vue() {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tlet boxWidth = 0,\n\t\t\t\t\ttextWidth = 0\n\t\t\t\t// 进行一定的延时\n\t\t\t\tawait uni.$u.sleep()\n\t\t\t\t// 查询盒子和文字的宽度\n\t\t\t\ttextWidth = (await this.$uGetRect('.u-notice__content__text')).width\n\t\t\t\tboxWidth = (await this.$uGetRect('.u-notice__content')).width\n\t\t\t\t// 根据t=s/v(时间=路程/速度)，这里为何不需要加上#u-notice-box的宽度，因为中设置了.u-notice-content样式中设置了padding-left: 100%\n\t\t\t\t// 恰巧计算出来的结果中已经包含了#u-notice-box的宽度\n\t\t\t\tthis.animationDuration = `${textWidth / uni.$u.getPx(this.speed)}s`\n\t\t\t\t// 这里必须这样开始动画，否则在APP上动画速度不会改变\n\t\t\t\tthis.animationPlayState = 'paused'\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.animationPlayState = 'running'\n\t\t\t\t}, 10)\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// nvue版处理\n\t\t\tasync nvue() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvueInit = false\n\t\t\t\tlet boxWidth = 0,\n\t\t\t\t\ttextWidth = 0\n\t\t\t\t// 进行一定的延时\n\t\t\t\tawait uni.$u.sleep()\n\t\t\t\t// 查询盒子和文字的宽度\n\t\t\t\ttextWidth = (await this.getNvueRect('u-notice__content__text')).width\n\t\t\t\tboxWidth = (await this.getNvueRect('u-notice__content')).width\n\t\t\t\t// 将文字移动到盒子的右边沿，之所以需要这么做，是因为nvue不支持100%单位，否则可以通过css设置\n\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\n\t\t\t\t\tstyles: {\n\t\t\t\t\t\ttransform: `translateX(${boxWidth}px)`\n\t\t\t\t\t},\n\t\t\t\t}, () => {\n\t\t\t\t\t// 如果非禁止动画，则开始滚动\n\t\t\t\t\t!this.stopAnimation && this.loopAnimation(textWidth, boxWidth)\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tloopAnimation(textWidth, boxWidth) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\n\t\t\t\t\tstyles: {\n\t\t\t\t\t\t// 目标移动终点为-textWidth，也即当文字的最右边贴到盒子的左边框的位置\n\t\t\t\t\t\ttransform: `translateX(-${textWidth}px)`\n\t\t\t\t\t},\n\t\t\t\t\t// 滚动时间的计算为，时间 = 路程(boxWidth + textWidth) / 速度，最后转为毫秒\n\t\t\t\t\tduration: (boxWidth + textWidth) / uni.$u.getPx(this.speed) * 1000,\n\t\t\t\t\tdelay: 10\n\t\t\t\t}, () => {\n\t\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\n\t\t\t\t\t\tstyles: {\n\t\t\t\t\t\t\t// 重新将文字移动到盒子的右边沿\n\t\t\t\t\t\t\ttransform: `translateX(${this.stopAnimation ? 0 : boxWidth}px)`\n\t\t\t\t\t\t},\n\t\t\t\t\t}, () => {\n\t\t\t\t\t\t// 如果非禁止动画，则继续下一轮滚动\n\t\t\t\t\t\tif (!this.stopAnimation) {\n\t\t\t\t\t\t\t// 判断是否需要初始化计算尺寸\n\t\t\t\t\t\t\tif (this.nvueInit) {\n\t\t\t\t\t\t\t\tthis.nvue()\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.loopAnimation(textWidth, boxWidth)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tgetNvueRect(el) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 返回一个promise\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tdom.getComponentRect(this.$refs[el], (res) => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 点击通告栏\n\t\t\tclickHandler(index) {\n\t\t\t\tthis.$emit('click')\n\t\t\t},\n\t\t\t// 点击右侧按钮，需要判断点击的是关闭图标还是箭头图标\n\t\t\tclose() {\n\t\t\t\tthis.$emit('close')\n\t\t\t}\n\t\t},\n\t\t// #ifdef APP-NVUE\n\t\tbeforeDestroy() {\n\t\t\tthis.stopAnimation = true\n\t\t},\n\t\t// #endif\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-notice {\n\t\t@include flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t&__left-icon {\n\t\t\talign-items: center;\n\t\t\tmargin-right: 5px;\n\t\t}\n\n\t\t&__right-icon {\n\t\t\tmargin-left: 5px;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&__content {\n\t\t\ttext-align: right;\n\t\t\tflex: 1;\n\t\t\t@include flex;\n\t\t\tflex-wrap: nowrap;\n\t\t\toverflow: hidden;\n\n\t\t\t&__text {\n\t\t\t\tfont-size: 14px;\n\t\t\t\tcolor: $u-warning;\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t// 这一句很重要，为了能让滚动左右连接起来\n\t\t\t\tpadding-left: 100%;\n\t\t\t\tword-break: keep-all;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tanimation: u-loop-animation 10s linear infinite both;\n\t\t\t\t/* #endif */\n\t\t\t\t@include flex(row);\n\t\t\t}\n\t\t}\n\n\t}\n\n\t@keyframes u-loop-animation {\n\t\t0% {\n\t\t\ttransform: translate3d(0, 0, 0);\n\t\t}\n\n\t\t100% {\n\t\t\ttransform: translate3d(-100%, 0, 0);\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=style&index=0&id=4661e472&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=style&index=0&id=4661e472&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755140700896\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}