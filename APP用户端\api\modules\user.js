import {
	req
} from '../../utils/req.js';
import siteInfo from '../../siteinfo.js';

export default {
	// 用户信息
	// userInfo(param) {
	// 	return req.get("/massage/app/IndexUser/userInfo", param)
	// },
	userInfo(param) {
		return req.get("user/info", param)
	},
	// loginuserInfo(param) {
	// 	return req.post("v1/wxLogin", param)
	// },
	loginuserInfo(param) {
		// 为微信登录接口添加定位字段支持
		return new Promise((resolve, reject) => {
			// 获取地理位置信息
			const locationData = this.getLocationData();
			const requestData = {
				...param,
				...locationData
			};

			// 使用uni.request直接调用，确保能添加定位字段
			uni.request({
				url: `${siteInfo.siteroot}user/login/wxLogin`,
				method: 'POST',
				data: requestData,
				header: {
					'Content-Type': 'application/json',
					'platform': 2, // 用户端
					'autograph': uni.getStorageSync('token') || ''
				},
				success: (res) => {
					// 构造响应对象，保持与原有格式一致
					const response = {
						code: res.data.code,
						msg: res.data.msg || res.data.error,
						data: res.data.data,
						header: res.header
					};
					resolve(response);
				},
				fail: (error) => {
					reject(new Error('网络请求失败'));
				}
			});
		});
	},
	updataInfo(param) {
		return req.post("user/update", param)
	},

	// 更新用户信息
	userUpdate(param) {
		return req.post("/massage/app/IndexUser/userUpdate", param)
	},
	// 获取手机号
	reportPhone(param) {
		return req.post("/massage/app/IndexUser/reportPhone", param)
	},
	// 验证码
	sendShortMsg(param) {
		return req.post("massage/app/IndexUser/sendShortMsg", param)
	},
	// 绑定手机号
	bindUserPhone(param) {
		return req.post("massage/app/IndexUser/bindUserPhone", param)
	},
	// 用户注册/绑定手机号
	register(param) {
		return req.post("user/register", param)
	},
	// 检查APP版本更新
	checkAppVersion(param) {
		return req.post("user/login/checkAppVersion", param)
	},

	// 获取地理位置数据的方法
	getLocationData() {
		try {
			// 从本地存储获取经纬度
			const lng = uni.getStorageSync('lng') || '';
			const lat = uni.getStorageSync('lat') || '';

			// 从Vuex store获取地址信息
			let address = '';
			try {
				// 尝试从store获取地址信息
				const store = getApp()?.globalData?.$store;
				if (store && store.state.service && store.state.service.regeocode) {
					address = store.state.service.regeocode.regeocode?.formatted_address || '';
				}
			} catch (storeError) {
				console.log('从store获取地址失败，尝试从本地存储获取');
			}

			// 如果store中没有地址，尝试从本地存储获取
			if (!address) {
				const storedRegeocode = uni.getStorageSync('regeocode');
				if (storedRegeocode && storedRegeocode.formatted_address) {
					address = storedRegeocode.formatted_address;
				}
			}

			// 返回登录接口需要的字段格式
			return {
				lng: lng || '',
				lat: lat || '',
				address: address || ''
			};
		} catch (error) {
			console.error('获取地理位置数据失败:', error);
			return {
				lng: '',
				lat: '',
				address: ''
			};
		}
	}
}
