<view class="page data-v-1ee6bdd6"><view class="header data-v-1ee6bdd6"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleHeader',['$0'],[[['list','',index]]]]]]]}}" class="header_item data-v-1ee6bdd6" bindtap="__e"><view style="{{(currentIndex===item.value?'color:#2E80FE;':'')}}" class="data-v-1ee6bdd6">{{item.name}}</view><view class="blue data-v-1ee6bdd6" style="{{(currentIndex===item.value?'':'background-color:#fff;')}}"></view></view></block></view><block wx:if="{{$root.g0===0}}"><u-empty vue-id="21080291-1" mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png" class="data-v-1ee6bdd6" bind:__l="__l"></u-empty></block><view data-event-opts="{{[['tap',[['dingyue']]]]}}" class="main data-v-1ee6bdd6" bindtap="__e"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-1ee6bdd6"><block wx:if="{{item.$orig.payType>=-1}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="main_item data-v-1ee6bdd6" bindtap="__e"><view class="head data-v-1ee6bdd6"><view class="no data-v-1ee6bdd6">{{"单号："+item.$orig.orderCode}}</view><view class="type data-v-1ee6bdd6">{{(item.$orig.payType===-1?'已取消':pay_typeArr[item.m0])+''}}</view></view><view class="mid data-v-1ee6bdd6"><view class="lef data-v-1ee6bdd6"><image src="{{item.$orig.goodsCover}}" mode class="data-v-1ee6bdd6"></image><text class="data-v-1ee6bdd6">{{item.$orig.goodsName}}</text><block wx:if="{{item.$orig.type===5}}"><text style="color:#F60100;" class="data-v-1ee6bdd6">活动订单</text></block></view><block wx:if="{{item.m1}}"><view class="righ data-v-1ee6bdd6"><view class="data-v-1ee6bdd6">{{"￥"+item.$orig.payPrice}}</view><view class="data-v-1ee6bdd6">{{"x"+(item.$orig.num?item.$orig.num:1)}}</view></view></block></view><view class="bot data-v-1ee6bdd6"><text class="data-v-1ee6bdd6">{{item.$orig.createTime}}</text><block wx:if="{{item.m2}}"><view data-event-opts="{{[['tap',[['gozhifu',['$0'],[[['orderList','',index]]]]]]]}}" class="qzf data-v-1ee6bdd6" catchtap="__e">去支付</view></block><block wx:if="{{item.m3}}"><view data-event-opts="{{[['tap',[['goUrl',['/user/huodongCashier?id='+item.$orig.id+'&price='+item.$orig.payPrice+'&type='+item.$orig.payType+'&goodsId='+item.$orig.goodsId]]]]]}}" class="qzf data-v-1ee6bdd6" catchtap="__e">去支付</view></block><block wx:if="{{item.m4}}"><view data-event-opts="{{[['tap',[['huodongquxiaos',['$0'],[[['orderList','',index]]]]]]]}}" class="qzf data-v-1ee6bdd6" catchtap="__e">取消订单</view></block><block wx:if="{{item.m5}}"><view data-event-opts="{{[['tap',[['confirmorder',['$0'],[[['orderList','',index]]]]]]]}}" class="qrwc data-v-1ee6bdd6" catchtap="__e">确认完成</view></block><block wx:if="{{item.m6}}"><view data-event-opts="{{[['tap',[['applyT',['$0'],[[['orderList','',index]]]]]]]}}" class="qrwc data-v-1ee6bdd6" catchtap="__e">申请退款</view></block><block wx:if="{{item.m7}}"><view data-event-opts="{{[['tap',[['huodongwanchengclick',['$0'],[[['orderList','',index]]]]]]]}}" class="qrwc data-v-1ee6bdd6" catchtap="__e">确认完成</view></block><block wx:if="{{item.m8}}"><view data-event-opts="{{[['tap',[['huodongclick']]]]}}" class="qrwc data-v-1ee6bdd6" style="color:#999999;background-color:#f0f0f0;" catchtap="__e">待上门</view></block><block wx:if="{{item.m9}}"><view data-event-opts="{{[['tap',[['gohuodongevaluate',['$0'],[[['orderList','',index]]]]]]]}}" class="qpl data-v-1ee6bdd6" catchtap="__e">去评价</view></block><block wx:if="{{item.m10}}"><view class="qpl data-v-1ee6bdd6">已评价</view></block><block wx:if="{{item.m11}}"><view data-event-opts="{{[['tap',[['goevaluate',['$0'],[[['orderList','',index]]]]]]]}}" class="qpl data-v-1ee6bdd6" catchtap="__e">去评价</view></block><block wx:if="{{item.m12}}"><view class="qpl data-v-1ee6bdd6">已评价</view></block></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['goChoose',['$0'],[[['orderList','',index]]]]]]]}}" class="main_item_already data-v-1ee6bdd6" bindtap="__e"><view class="title data-v-1ee6bdd6" style="font-size:32rpx;font-weight:500;">{{''+(item.g1===0?'等待师傅报价':'等待您选择师傅')+''}}</view><block wx:if="{{item.g2>0}}"><view class="ok data-v-1ee6bdd6">已有师傅报价</view></block><view class="no data-v-1ee6bdd6">{{"单号："+item.$orig.orderCode}}</view><view class="mid data-v-1ee6bdd6"><view class="lef data-v-1ee6bdd6"><image src="{{item.$orig.goodsCover}}" mode class="data-v-1ee6bdd6"></image><text class="data-v-1ee6bdd6">{{item.$orig.goodsName}}</text></view></view><view class="bot data-v-1ee6bdd6"><text class="data-v-1ee6bdd6">{{item.$orig.createTime}}</text></view><view class="shifu data-v-1ee6bdd6"><scroll-view scroll-x="true" class="data-v-1ee6bdd6"><block wx:for="{{item.l0}}" wx:for-item="shfItem" wx:for-index="shfIndex" wx:key="shfIndex"><view class="shifu_item data-v-1ee6bdd6"><image src="{{shfItem.$orig.selfImg?shfItem.$orig.selfImg:'/static/mine/default_user.png'}}" mode="aspectFit" class="data-v-1ee6bdd6"></image><text class="data-v-1ee6bdd6">{{"￥"+shfItem.g3}}</text></view></block></scroll-view></view><block wx:if="{{item.g4>0}}"><view style="display:flex;justify-content:center;align-items:center;margin-top:20rpx;" class="data-v-1ee6bdd6"><view data-event-opts="{{[['tap',[['cancelorder',['$0'],[[['orderList','',index]]]]]]]}}" class="qxdd data-v-1ee6bdd6" catchtap="__e">取消订单</view><view class="tips data-v-1ee6bdd6" style="margin-left:20%;" vif="item.quotedPriceVos.length > 0">{{''+item.g5+'位师傅已报价'}}</view><view class="qxdd data-v-1ee6bdd6" style="margin-left:20rpx;">选择师傅</view></view></block><block wx:if="{{item.m13}}"><view data-event-opts="{{[['tap',[['cancelorder',['$0'],[[['orderList','',index]]]]]]]}}" class="qxdd data-v-1ee6bdd6" catchtap="__e">取消订单</view></block></view></block></view></block></view><u-modal vue-id="21080291-2" show="{{showCancel}}" title="取消订单" content="确认要取消该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e1']]],['^confirm',[['confirmCancel']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l"></u-modal><u-modal vue-id="21080291-3" show="{{showConfirm}}" title="完成订单" content="确认要完成该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e2']]],['^confirm',[['confirmconfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l"></u-modal><u-modal vue-id="21080291-4" show="{{showPaymentModal}}" title="提示" showCancelButton="{{true}}" confirm-text="去支付" data-event-opts="{{[['^cancel',[['e3']]],['^confirm',[['confirmPayment']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l" vue-slots="{{['default']}}"><view class="modal-content-red data-v-1ee6bdd6">{{''+paymentRemind+''}}</view></u-modal><u-modal vue-id="21080291-5" show="{{showRefundModal}}" title="提示" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e4']]],['^confirm',[['confirmRefund']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l" vue-slots="{{['default']}}"><view class="modal-content-red data-v-1ee6bdd6">{{''+reminddata+''}}</view></u-modal><block wx:if="{{$root.g6>=10}}"><view style="display:flex;justify-content:center;" class="data-v-1ee6bdd6"><u-loadmore vue-id="21080291-6" status="{{status}}" class="data-v-1ee6bdd6" bind:__l="__l"></u-loadmore></view></block></view>